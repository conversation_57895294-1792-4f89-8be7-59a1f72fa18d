# spiders/books_spider.py
import scrapy

class BooksSpider(scrapy.Spider):
    name = 'books'
    # Using books.toscrape.com - a website made for scraping practice
    start_urls = ['http://books.toscrape.com/']

    def parse(self, response):
        # Extract data from each book listing on the page
        for book in response.css('article.product_pod'):
            yield {
                'title': book.css('h3 a::attr(title)').get(),
                'price': book.css('p.price_color::text').get(),
                'availability': book.css('p.availability::text').get(),
                'rating': book.css('p.star-rating::attr(class)').get().split()[-1]
            }

        # Follow the link to the next page
        next_page = response.css('li.next a::attr(href)').get()
        if next_page is not None:
            next_page_url = response.urljoin(next_page)
            yield scrapy.Request(next_page_url, callback=self.parse)

# settings.py
BOT_NAME = 'bookstore_scraper'

SPIDER_MODULES = ['bookstore_scraper.spiders']
NEWSPIDER_MODULE = 'bookstore_scraper.spiders'

# Add a delay between requests
DOWNLOAD_DELAY = 1

# Respect robots.txt
ROBOTSTXT_OBEY = True

# Configure user agent
USER_AGENT = 'Mozilla/5.0 (compatible; MyBookScraperBot/1.0)'

# Enable logging
LOG_ENABLED = True
LOG_LEVEL = 'INFO'
