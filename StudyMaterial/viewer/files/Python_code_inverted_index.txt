# Sample documents to build the inverted index
documents = {
    1: "the quick brown fox jumps over the lazy dog",
    2: "the quick blue fox",
    3: "the lazy dog sleeps",
    4: "blue sky and brown earth"
}

# Step 1: Build the Inverted Index
def build_inverted_index(docs):
    inverted_index = {}
    for doc_id, text in docs.items():
        words = text.split()  # Tokenize the document
        for word in words:
            word = word.lower()  # Make it case-insensitive
            if word in inverted_index:
                if doc_id not in inverted_index[word]:
                    inverted_index[word].append(doc_id)
            else:
                inverted_index[word] = [doc_id]
    return inverted_index

# Step 2: Search function to find documents that contain the search term
def search(query, inverted_index):
    query = query.lower()  # Make the query case-insensitive
    if query in inverted_index:
        return inverted_index[query]
    else:
        return []

# Building the inverted index from the sample documents
inverted_index = build_inverted_index(documents)

# Example search queries
print("Inverted Index:", inverted_index)  # Display the inverted index

query = "quick"
result = search(query, inverted_index)
print(f"\nDocuments containing the word '{query}': {result}")

query = "blue"
result = search(query, inverted_index)
print(f"\nDocuments containing the word '{query}': {result}")

query = "earth"
result = search(query, inverted_index)
print(f"\nDocuments containing the word '{query}': {result}")
