import numpy as np

# Step 1: Create a link matrix (adjacency matrix)
# Each element M[i][j] represents a link from page j to page i
link_matrix = np.array([
    [0, 1, 1, 0],  # Page 1 is linked by Page 2 and Page 3
    [1, 0, 0, 1],  # Page 2 is linked by Page 1 and Page 4
    [1, 1, 0, 1],  # Page 3 is linked by Page 1, Page 2, and Page 4
    [0, 1, 0, 0],  # Page 4 is linked by Page 2
])

# Step 2: Define the PageRank function
def page_rank(M, num_iterations=100, d=0.85):
    # Number of pages
    N = M.shape[1]
    
    # Step 3: Initialize the PageRank vector with equal values for each page
    rank = np.ones(N) / N
    
    # Step 4: Create the damping factor matrix
    # Damping factor allows a small probability (1 - d) of jumping to any page randomly
    teleport = np.ones(N) / N
    
    # Step 5: Iterate the PageRank calculation
    for i in range(num_iterations):
        rank = d * np.dot(M, rank) + (1 - d) * teleport
    
    return rank

# Step 6: Normalize the link matrix
# Each column in the link matrix should sum to 1 (indicating the probability of following a link)
column_sums = np.sum(link_matrix, axis=0)
M = link_matrix / column_sums

# Step 7: Calculate PageRank scores
page_ranks = page_rank(M)

# Output the PageRank scores
for i, score in enumerate(page_ranks, 1):
    print(f"Page {i}: {score:.4f}")
