import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.cluster import AgglomerativeClustering
import matplotlib.pyplot as plt
from scipy.cluster.hierarchy import dendrogram

# Sample documents
documents = [
    "The quick brown fox jumps over the lazy dog",
    "A fox is a cunning and adaptable animal",
    "Dogs are known for their loyalty and friendliness",
    "Cats are independent and often aloof pets",
    "Lions are large cats and apex predators",
    "Wolves hunt in packs and are related to dogs"
]

# Preprocess and vectorize the documents
vectorizer = TfidfVectorizer(stop_words='english')
X = vectorizer.fit_transform(documents)

# Perform hierarchical clustering
n_clusters = 3
clustering = AgglomerativeClustering(n_clusters=n_clusters)
clustering.fit(X.toarray())

# Function to plot the dendrogram
def plot_dendrogram(model, **kwargs):
    counts = np.zeros(model.children_.shape[0])
    n_samples = len(model.labels_)
    for i, merge in enumerate(model.children_):
        current_count = 0
        for child_idx in merge:
            if child_idx < n_samples:
                current_count += 1
            else:
                current_count += counts[child_idx - n_samples]
        counts[i] = current_count

    linkage_matrix = np.column_stack([model.children_, model.distances_, counts]).astype(float)

    plt.figure(figsize=(10, 7))
    dendrogram(linkage_matrix, **kwargs)
    plt.title('Hierarchical Clustering Dendrogram')
    plt.xlabel('Sample index or (cluster size)')
    plt.ylabel('Distance')
    plt.show()

# Create a new AgglomerativeClustering object for plotting
plot_clustering = AgglomerativeClustering(distance_threshold=0, n_clusters=None)
plot_clustering.fit(X.toarray())

# Plot the dendrogram
plot_dendrogram(plot_clustering, truncate_mode='level', p=3)

# Print the clusters
for i in range(n_clusters):
    print(f"Cluster {i + 1}:")
    for j, label in enumerate(clustering.labels_):
        if label == i:
            print(f"  - {documents[j]}")
    print()
