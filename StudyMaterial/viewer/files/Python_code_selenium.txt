from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import json
import time

def setup_driver():
    """Set up Chrome WebDriver with options"""
    chrome_options = Options()
    # Uncomment below line to run in headless mode
    # chrome_options.add_argument('--headless')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    return webdriver.Chrome(options=chrome_options)

def scrape_dynamic_quotes():
    """Scrape quotes that are loaded dynamically with JavaScript"""
    driver = setup_driver()
    quotes = []
    
    try:
        # Navigate to the JavaScript-delayed quotes page
        print("Navigating to the website...")
        driver.get("http://quotes.toscrape.com/js-delayed")
        
        # Wait for the dynamic content to load
        print("Waiting for dynamic content to load...")
        wait = WebDriverWait(driver, 10)
        
        # Wait for the loading message to disappear
        wait.until_not(
            EC.presence_of_element_located((By.CSS_SELECTOR, ".loading-quote"))
        )
        
        # Wait for quotes to appear
        quote_elements = wait.until(
            EC.presence_of_all_elements_located((By.CSS_SELECTOR, ".quote"))
        )
        
        print(f"Found {len(quote_elements)} quotes")
        
        # Extract quote information
        for quote in quote_elements:
            try:
                # Get quote text
                text = quote.find_element(By.CSS_SELECTOR, ".text").text
                
                # Get author
                author = quote.find_element(By.CSS_SELECTOR, ".author").text
                
                # Get tags
                tags = [tag.text for tag in quote.find_elements(By.CSS_SELECTOR, ".tag")]
                
                quotes.append({
                    "text": text,
                    "author": author,
                    "tags": tags
                })
                
                print(f"Scraped quote by: {author}")
                
            except Exception as e:
                print(f"Error extracting quote details: {str(e)}")
                continue
        
        # Save results to JSON file
        with open("dynamic_quotes.json", "w", encoding="utf-8") as f:
            json.dump(quotes, f, indent=4, ensure_ascii=False)
            
        print(f"\nSuccessfully scraped {len(quotes)} quotes")
        
        # Demonstrate interacting with dynamic elements
        try:
            # Click the "Next" button if it exists
            next_button = driver.find_element(By.CSS_SELECTOR, ".next a")
            print("\nFound next page button, clicking...")
            next_button.click()
            
            # Wait for new content to load after click
            time.sleep(2)
            print("Loaded next page successfully")
            
        except Exception as e:
            print("No more pages to load")
        
    except Exception as e:
        print(f"An error occurred: {str(e)}")
    
    finally:
        driver.quit()

def main():
    print("Starting dynamic web scraping demonstration...")
    scrape_dynamic_quotes()
    print("Scraping completed! Check dynamic_quotes.json for results")

if __name__ == "__main__":
    main()
