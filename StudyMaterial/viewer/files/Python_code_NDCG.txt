import numpy as np

# Function to compute DCG (Discounted Cumulative Gain)
def dcg(relevance_scores):
    dcg_value = 0.0
    for i, rel in enumerate(relevance_scores):
        # i is the position, and rel is the relevance score
        dcg_value += (2 ** rel - 1) / np.log2(i + 2)
    return dcg_value

# Function to compute NDCG (Normalized Discounted Cumulative Gain)
def ndcg(relevance_scores, k=None):
    # Sort the relevance scores in decreasing order to get the ideal DCG (IDCG)
    ideal_relevance_scores = sorted(relevance_scores, reverse=True)
    
    # Compute DCG and IDCG
    actual_dcg = dcg(relevance_scores[:k])
    ideal_dcg = dcg(ideal_relevance_scores[:k])
    
    # Avoid division by zero if ideal_dcg is zero
    if ideal_dcg == 0:
        return 0.0
    
    # Return the normalized DCG
    return actual_dcg / ideal_dcg

# Example usage
# Relevance scores for search results (higher numbers mean more relevant)
relevance_scores = [3, 2, 3, 0, 1, 2]  # Example search result relevance ratings
k = 5  # Evaluate NDCG at rank position 5

# Calculate NDCG
ndcg_value = ndcg(relevance_scores, k)
print(f"NDCG@{k}: {ndcg_value:.4f}")
