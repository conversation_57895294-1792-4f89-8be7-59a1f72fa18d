import numpy as np
from sklearn.feature_extraction.text import CountVectorizer
from sklearn.preprocessing import normalize

# Sample documents
documents = [
    'I enjoy coding in Python and JavaScript',
    'Python and JavaScript are great programming languages',
    'I enjoy solving problems using algorithms',
    'Algorithms are important for programming and data science',
    'Data science and machine learning are closely related',
    'Machine learning is a key part of data science'
]

# Step 1: Vectorize the text data (Bag of Words)
vectorizer = CountVectorizer(stop_words='english')
X = vectorizer.fit_transform(documents).toarray()

# Vocabulary and document-term matrix
vocabulary = vectorizer.get_feature_names_out()
print("Vocabulary:", vocabulary)
print("\nDocument-Term Matrix (X):\n", X)

# Step 2: Initialize parameters for PLSA
n_docs, n_words = X.shape
n_topics = 2  # Number of topics
max_iter = 100  # Maximum number of iterations for the EM algorithm

# Random initialization of probabilities
np.random.seed(0)
P_w_z = np.random.rand(n_topics, n_words)  # P(w|z): word distribution for each topic
P_z_d = np.random.rand(n_docs, n_topics)   # P(z|d): topic distribution for each document

# Normalize to ensure they are proper probability distributions
P_w_z = normalize(P_w_z, norm='l1', axis=1)  # Normalize rows to sum to 1
P_z_d = normalize(P_z_d, norm='l1', axis=1)  # Normalize rows to sum to 1

# Expectation-Maximization algorithm
for _ in range(max_iter):
    # E-step: Compute P(z|d,w) = P(w|z) * P(z|d) / sum_z(P(w|z) * P(z|d))
    P_z_dw = np.zeros((n_docs, n_words, n_topics))  # P(z|d,w): posterior probability
    
    for d in range(n_docs):
        for w in range(n_words):
            denominator = np.dot(P_z_d[d, :], P_w_z[:, w])
            if denominator > 0:
                for z in range(n_topics):
                    P_z_dw[d, w, z] = P_z_d[d, z] * P_w_z[z, w] / denominator
    
    # M-step: Update P(w|z) and P(z|d)
    P_w_z = np.zeros((n_topics, n_words))
    P_z_d = np.zeros((n_docs, n_topics))
    
    # Update P(w|z)
    for z in range(n_topics):
        for w in range(n_words):
            for d in range(n_docs):
                P_w_z[z, w] += X[d, w] * P_z_dw[d, w, z]
        P_w_z[z, :] /= np.sum(P_w_z[z, :])  # Normalize
    
    # Update P(z|d)
    for d in range(n_docs):
        for z in range(n_topics):
            for w in range(n_words):
                P_z_d[d, z] += X[d, w] * P_z_dw[d, w, z]
        P_z_d[d, :] /= np.sum(P_z_d[d, :])  # Normalize

# Final topic-word and document-topic distributions
print("\nFinal P(w|z) (Word Distribution per Topic):")
print(P_w_z)
print("\nFinal P(z|d) (Topic Distribution per Document):")
print(P_z_d)

# Step 3: Interpret the topics
print("\nTopics and Top Words:")
n_top_words = 5  # Show top 5 words for each topic
for z in range(n_topics):
    top_words_idx = np.argsort(P_w_z[z, :])[-n_top_words:]
    top_words = [vocabulary[i] for i in top_words_idx]
    print(f"Topic {z+1}: {top_words}")
