# import the libraries
import requests
from bs4 import BeautifulSoup

# URL of the page to scrape
url = "https://creative-wisdom.com/computer/sas/parametric.html"

# Send a GET request to fetch the HTML content
response = requests.get(url)

# Check if the request was successful
if response.status_code == 200:
    # Parse the HTML content with BeautifulSoup
    soup = BeautifulSoup(response.text, 'html.parser')
    
    # Extract the title of the page
    title = soup.title.string
    print(f"Title of the page: {title}\n")
    
    # Extract all headers (h1, h2, etc.)
    headers = soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
    print("Headers on the page:")
    for header in headers:
        print(f"{header.name}: {header.get_text(strip=True)}")
    
    # Extract all paragraphs
    paragraphs = soup.find_all('p')
    print("\nParagraphs on the page:")
    for i, paragraph in enumerate(paragraphs, start=1):
        print(f"Paragraph {i}: {paragraph.get_text(strip=True)}\n")
else:
    print(f"Failed to retrieve the page. Status code: {response.status_code}")
