# Install gensim
pip install gensim

from gensim.models import Word2Vec
from nltk.tokenize import word_tokenize
from nltk.corpus import stopwords
import string
import nltk

# Download NLTK resources
nltk.download('punkt')
nltk.download('stopwords')

# Example corpus
corpus = [
    "Artificial intelligence is transforming the world.",
    "Data science involves data analysis and machine learning.",
    "Artificial intelligence and machine learning are closely related fields.",
    "The future of artificial intelligence and data science is exciting.",
    "Ethics in artificial intelligence is a critical area of discussion."
]

# Preprocess the text: tokenization, lowercasing, and removing stopwords/punctuation
stop_words = set(stopwords.words('english'))
corpus_tokenized = [
    [word.lower() for word in word_tokenize(sentence) if word.lower() not in stop_words and word not in string.punctuation]
    for sentence in corpus
]

# Train Word2Vec model
model = Word2Vec(corpus_tokenized, vector_size=100, window=5, min_count=1, sg=1)  # sg=1 for Skip-gram

# Example: Finding similar words
similar_words = model.wv.most_similar('artificial')
print("Words most similar to 'artificial':")
print(similar_words)

# Example: Word vector for a given word
word_vector = model.wv['intelligence']
print("\nVector for the word 'intelligence':")
print(word_vector)
