# Import necessary libraries
from sklearn.datasets import fetch_20newsgroups
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.model_selection import train_test_split
from sklearn.svm import SVC
from sklearn.metrics import classification_report, accuracy_score

# Load the 20 Newsgroups dataset
categories = ['alt.atheism', 'sci.space']  # For simplicity, we select two categories
newsgroups = fetch_20newsgroups(subset='all', categories=categories)

# Vectorize the text data using TF-IDF
vectorizer = TfidfVectorizer(stop_words='english')  # Convert text to TF-IDF feature vectors
X = vectorizer.fit_transform(newsgroups.data)  # Feature matrix
y = newsgroups.target  # Labels (categories)

# Split the dataset into training and testing sets
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# Initialize and train the SVM classifier
svm_model = SVC(kernel='linear')  # Using a linear kernel
svm_model.fit(X_train, y_train)

# Make predictions on the test set
y_pred = svm_model.predict(X_test)

# Evaluate the model
print("Accuracy:", accuracy_score(y_test, y_pred))
print("\nClassification Report:\n", classification_report(y_test, y_pred, target_names=newsgroups.target_names))
