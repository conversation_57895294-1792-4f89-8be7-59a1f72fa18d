# Import necessary libraries
from sklearn.feature_extraction.text import CountVectorizer
from sklearn.model_selection import train_test_split
from sklearn.naive_bayes import MultinomialNB
from sklearn.metrics import accuracy_score

# Sample email dataset (you can replace this with a real dataset)
emails = [
    "Congratulations! You've won a $1000 Walmart gift card. Click here to claim now.",  # spam
    "Hi, <PERSON>, are we still meeting for lunch tomorrow?",  # non-spam
    "You are selected for a chance to get a free iPhone. Claim now!",  # spam
    "Please find the attached report for last quarter's sales.",  # non-spam
    "Winner! You've been chosen for a free vacation to the Bahamas!",  # spam
    "Can you send me the project details by end of today?",  # non-spam
    "Limited time offer! Get 50% off on all products. Shop now!",  # spam
    "Let's schedule a meeting to go over the new marketing strategy.",  # non-spam
]

# Labels (1 for spam, 0 for non-spam)
labels = [1, 0, 1, 0, 1, 0, 1, 0]

# Step 1: Preprocess the text data using CountVectorizer
vectorizer = CountVectorizer()
email_counts = vectorizer.fit_transform(emails)

# Step 2: Split the dataset into training and test sets (80% train, 20% test)
X_train, X_test, y_train, y_test = train_test_split(email_counts, labels, test_size=0.2, random_state=42)

# Step 3: Train the Naive Bayes model
nb_classifier = MultinomialNB()
nb_classifier.fit(X_train, y_train)

# Step 4: Predict on the test set
y_pred = nb_classifier.predict(X_test)

# Step 5: Evaluate the model
accuracy = accuracy_score(y_test, y_pred)
print(f"Accuracy: {accuracy * 100:.2f}%")

# Step 6: Test the model with new examples
new_emails = [
    "You have won a prize! Click here to claim your reward.",  # likely spam
    "Can you please review the attached document and let me know your feedback?",  # likely non-spam
]

new_email_counts = vectorizer.transform(new_emails)
predictions = nb_classifier.predict(new_email_counts)

for email, label in zip(new_emails, predictions):
    print(f"Email: '{email}' -> {'Spam' if label == 1 else 'Not Spam'}")
