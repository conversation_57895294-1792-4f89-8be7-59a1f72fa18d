import numpy as np
import pandas as pd
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.decomposition import NMF

# Sample corpus
documents = [
    "Artificial intelligence and machine learning are transforming industries.",
    "The field of data science is growing rapidly.",
    "Machine learning and data science are revolutionizing technology.",
    "Big data and AI are transforming healthcare and finance.",
    "Data science and AI are crucial in modern technological advancements.",
    "Deep learning is a subset of machine learning in AI."
]

# Step 1: Create a document-term matrix using TF-IDF
vectorizer = TfidfVectorizer(stop_words='english', max_features=20)  # Restricting to top 20 features for simplicity
tfidf_matrix = vectorizer.fit_transform(documents)

# Step 2: Apply NMF to extract topics
nmf_model = NMF(n_components=2, random_state=42)  # Extracting 2 topics for illustration
W = nmf_model.fit_transform(tfidf_matrix)  # Document-topic matrix
H = nmf_model.components_  # Topic-word matrix

# Step 3: Display the topics
feature_names = vectorizer.get_feature_names_out()

def display_topics(H, feature_names, num_top_words):
    for topic_idx, topic in enumerate(H):
        print(f"Topic #{topic_idx + 1}:")
        print(" ".join([feature_names[i] for i in topic.argsort()[:-num_top_words - 1:-1]]))

# Display the top words for each topic
num_top_words = 5
display_topics(H, feature_names, num_top_words)

# Optional: Display the document-topic matrix
print("\nDocument-Topic Matrix (W):")
print(pd.DataFrame(W, columns=[f"Topic {i+1}" for i in range(W.shape[1])]))
