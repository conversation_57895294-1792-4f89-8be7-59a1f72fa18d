# Import necessary libraries
from sklearn.feature_extraction.text import TfidfVectorizer

# Sample documents
documents = [
    "Text mining is extracting useful information from text.",
    "Text preprocessing is an essential step in text mining.",
    "The Bag of Words model is simple but effective.",
    "TF-IDF helps determine the importance of words in a document.",
    "Word embeddings capture the meaning of words in context."
]

# Initialize the TfidfVectorizer
tfidf_vectorizer = TfidfVectorizer()

# Fit the model and transform the documents into a TF-IDF matrix
tfidf_matrix = tfidf_vectorizer.fit_transform(documents)

# Get the feature names (i.e., words)
feature_names = tfidf_vectorizer.get_feature_names_out()

# Convert the TF-IDF matrix to a dense array for easy viewing
tfidf_dense = tfidf_matrix.todense()

# Print the TF-IDF values for each document
for i, doc in enumerate(documents):
    print(f"Document {i+1}:")
    for j, word in enumerate(feature_names):
        print(f"{word}: {tfidf_dense[i, j]:.3f}")
    print("\n")
