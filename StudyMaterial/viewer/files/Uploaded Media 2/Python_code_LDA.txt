import nltk
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize
from gensim import corpora
from gensim.models import LdaModel

# Download NLTK data (only need to run once)
nltk.download('punkt')
nltk.download('stopwords')

# Sample documents
documents = [
    "The game of football is so exciting",
    "I love to watch movies on weekends",
    "Data science is a fast-growing field",
    "Artificial intelligence and machine learning are revolutionizing technology",
    "I play football every weekend",
    "Machine learning models are used for data analysis"
]

# Text preprocessing: Tokenization and removal of stopwords
stop_words = set(stopwords.words('english'))

def preprocess(text):
    tokens = word_tokenize(text.lower())  # Convert to lowercase and tokenize
    return [word for word in tokens if word.isalnum() and word not in stop_words]

# Preprocess each document
processed_docs = [preprocess(doc) for doc in documents]

# Create a dictionary and a corpus
dictionary = corpora.Dictionary(processed_docs)
corpus = [dictionary.doc2bow(doc) for doc in processed_docs]

# Train the LDA model
num_topics = 2  # Number of topics to extract
lda_model = LdaModel(corpus, num_topics=num_topics, id2word=dictionary, passes=15)

# Print the topics discovered
for idx, topic in lda_model.print_topics(num_words=5):
    print(f"Topic {idx}: {topic}")

# Infer topic distribution for a new document
new_doc = "I enjoy learning about data and artificial intelligence"
bow = dictionary.doc2bow(preprocess(new_doc))
topics = lda_model.get_document_topics(bow)
print(f"\nTopic distribution for the new document: {topics}")
