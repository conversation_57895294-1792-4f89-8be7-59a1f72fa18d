"""
Qualitative Assessment Module
DSCI 6700 Final Project - Part 3.2

This module performs comprehensive qualitative assessment of traditional text mining
and multimodal AI approaches, evaluating aspects beyond quantitative metrics such as
interpretability, usability, ethical considerations, and practical implementation factors.

Features:
- Interpretability and explainability analysis
- Usability and user experience evaluation
- Ethical considerations and bias assessment
- Implementation complexity analysis
- Cost-benefit evaluation
- Stakeholder perspective analysis
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import json
import os
from datetime import datetime
from typing import Dict, List, Any, Tuple
import warnings
warnings.filterwarnings('ignore')

# Set style for visualizations
plt.style.use('default')
sns.set_palette("husl")

class QualitativeAssessment:
    """
    Comprehensive qualitative assessment of text mining approaches.
    """
    
    def __init__(self):
        """Initialize the qualitative assessment framework."""
        self.assessment_results = {}
        self.evaluation_criteria = self._define_evaluation_criteria()
        
    def _define_evaluation_criteria(self):
        """Define comprehensive evaluation criteria for qualitative assessment."""
        return {
            'interpretability': {
                'description': 'Ability to understand and explain model decisions',
                'weight': 0.20,
                'subcriteria': ['transparency', 'explainability', 'debuggability']
            },
            'usability': {
                'description': 'Ease of use and user experience',
                'weight': 0.15,
                'subcriteria': ['learning_curve', 'interface_quality', 'documentation']
            },
            'reliability': {
                'description': 'Consistency and dependability of results',
                'weight': 0.18,
                'subcriteria': ['reproducibility', 'stability', 'error_handling']
            },
            'scalability': {
                'description': 'Ability to handle increasing data volumes and complexity',
                'weight': 0.15,
                'subcriteria': ['computational_efficiency', 'memory_usage', 'parallelization']
            },
            'flexibility': {
                'description': 'Adaptability to different domains and tasks',
                'weight': 0.12,
                'subcriteria': ['customization', 'extensibility', 'domain_adaptation']
            },
            'ethical_considerations': {
                'description': 'Ethical implications and bias considerations',
                'weight': 0.10,
                'subcriteria': ['bias_mitigation', 'fairness', 'privacy_protection']
            },
            'implementation_complexity': {
                'description': 'Complexity of deployment and maintenance',
                'weight': 0.10,
                'subcriteria': ['setup_complexity', 'maintenance_effort', 'skill_requirements']
            }
        }
    
    def assess_interpretability(self):
        """Assess interpretability and explainability of both approaches."""
        print("\n" + "="*60)
        print("INTERPRETABILITY AND EXPLAINABILITY ASSESSMENT")
        print("="*60)
        
        interpretability_scores = {
            'Traditional Methods': {
                'TF-IDF': {
                    'transparency': 9,  # Very transparent - clear mathematical foundation
                    'explainability': 8,  # Easy to explain feature importance
                    'debuggability': 9,  # Easy to debug and understand failures
                    'overall_score': 8.7,
                    'strengths': ['Clear mathematical foundation', 'Feature weights interpretable', 'Deterministic results'],
                    'weaknesses': ['Limited semantic understanding', 'Sparse representations']
                },
                'LDA': {
                    'transparency': 7,  # Probabilistic model is interpretable
                    'explainability': 8,  # Topic-word distributions are explainable
                    'debuggability': 7,  # Can analyze topic quality
                    'overall_score': 7.3,
                    'strengths': ['Topic interpretability', 'Probabilistic foundation', 'Human-readable topics'],
                    'weaknesses': ['Parameter sensitivity', 'Topic coherence varies']
                },
                'Naive Bayes/SVM': {
                    'transparency': 8,  # Clear decision boundaries
                    'explainability': 7,  # Feature importance available
                    'debuggability': 8,  # Easy to analyze misclassifications
                    'overall_score': 7.7,
                    'strengths': ['Clear decision logic', 'Feature importance', 'Well-understood algorithms'],
                    'weaknesses': ['Limited context understanding', 'Feature engineering dependent']
                }
            },
            'AI Methods': {
                'Gemini Multimodal': {
                    'transparency': 3,  # Black box nature
                    'explainability': 4,  # Limited explainability mechanisms
                    'debuggability': 3,  # Difficult to debug failures
                    'overall_score': 3.3,
                    'strengths': ['High-level reasoning explanations', 'Context-aware responses', 'Natural language explanations'],
                    'weaknesses': ['Black box decisions', 'Limited internal visibility', 'Complex failure modes']
                }
            }
        }
        
        print("Interpretability Assessment Results:")
        print("-" * 40)
        
        for approach, methods in interpretability_scores.items():
            print(f"\n{approach}:")
            for method, scores in methods.items():
                print(f"  {method}:")
                print(f"    Overall Score: {scores['overall_score']:.1f}/10")
                print(f"    Transparency: {scores['transparency']}/10")
                print(f"    Explainability: {scores['explainability']}/10")
                print(f"    Debuggability: {scores['debuggability']}/10")
                print(f"    Strengths: {', '.join(scores['strengths'])}")
                print(f"    Weaknesses: {', '.join(scores['weaknesses'])}")
        
        # Calculate average scores
        traditional_avg = np.mean([m['overall_score'] for m in interpretability_scores['Traditional Methods'].values()])
        ai_avg = np.mean([m['overall_score'] for m in interpretability_scores['AI Methods'].values()])
        
        print(f"\nSummary:")
        print(f"Traditional Methods Average: {traditional_avg:.1f}/10")
        print(f"AI Methods Average: {ai_avg:.1f}/10")
        print(f"Interpretability Advantage: Traditional Methods (+{traditional_avg - ai_avg:.1f} points)")
        
        self.assessment_results['interpretability'] = interpretability_scores
        return interpretability_scores
    
    def assess_usability(self):
        """Assess usability and user experience factors."""
        print("\n" + "="*60)
        print("USABILITY AND USER EXPERIENCE ASSESSMENT")
        print("="*60)
        
        usability_scores = {
            'Traditional Methods': {
                'learning_curve': 6,  # Moderate - requires statistical knowledge
                'interface_quality': 7,  # Good - established libraries
                'documentation': 8,  # Excellent - well-documented
                'setup_complexity': 7,  # Moderate setup
                'user_control': 9,  # High user control over parameters
                'overall_score': 7.4,
                'user_feedback': [
                    'Requires statistical background',
                    'Good control over parameters',
                    'Extensive documentation available',
                    'Predictable behavior'
                ]
            },
            'AI Methods': {
                'learning_curve': 8,  # Easier for non-experts
                'interface_quality': 9,  # Excellent - intuitive interfaces
                'documentation': 7,  # Good but evolving
                'setup_complexity': 6,  # Can be complex to set up
                'user_control': 5,  # Limited control over internal processes
                'overall_score': 7.0,
                'user_feedback': [
                    'More intuitive for non-experts',
                    'Limited parameter control',
                    'Rapidly evolving documentation',
                    'Sometimes unpredictable results'
                ]
            }
        }
        
        print("Usability Assessment Results:")
        print("-" * 30)
        
        for approach, scores in usability_scores.items():
            print(f"\n{approach}:")
            print(f"  Overall Score: {scores['overall_score']:.1f}/10")
            print(f"  Learning Curve: {scores['learning_curve']}/10")
            print(f"  Interface Quality: {scores['interface_quality']}/10")
            print(f"  Documentation: {scores['documentation']}/10")
            print(f"  Setup Complexity: {scores['setup_complexity']}/10")
            print(f"  User Control: {scores['user_control']}/10")
            print(f"  User Feedback:")
            for feedback in scores['user_feedback']:
                print(f"    - {feedback}")
        
        self.assessment_results['usability'] = usability_scores
        return usability_scores
    
    def assess_ethical_considerations(self):
        """Assess ethical implications and bias considerations."""
        print("\n" + "="*60)
        print("ETHICAL CONSIDERATIONS AND BIAS ASSESSMENT")
        print("="*60)
        
        ethical_assessment = {
            'Traditional Methods': {
                'bias_sources': [
                    'Training data bias',
                    'Feature selection bias',
                    'Sampling bias'
                ],
                'bias_mitigation': {
                    'detectability': 8,  # Easy to detect bias in features
                    'controllability': 9,  # High control over bias mitigation
                    'transparency': 9,  # Transparent bias sources
                    'score': 8.7
                },
                'privacy_protection': {
                    'data_handling': 8,  # Local processing possible
                    'model_privacy': 9,  # Model parameters are transparent
                    'inference_privacy': 8,  # Controlled inference process
                    'score': 8.3
                },
                'fairness': {
                    'group_fairness': 7,  # Can be assessed and controlled
                    'individual_fairness': 7,  # Deterministic treatment
                    'procedural_fairness': 9,  # Transparent procedures
                    'score': 7.7
                },
                'overall_ethical_score': 8.2
            },
            'AI Methods': {
                'bias_sources': [
                    'Training data bias (large-scale)',
                    'Model architecture bias',
                    'Reinforcement learning bias',
                    'Cultural and linguistic bias'
                ],
                'bias_mitigation': {
                    'detectability': 4,  # Difficult to detect bias sources
                    'controllability': 3,  # Limited control over bias
                    'transparency': 2,  # Opaque bias mechanisms
                    'score': 3.0
                },
                'privacy_protection': {
                    'data_handling': 5,  # Often requires cloud processing
                    'model_privacy': 3,  # Proprietary models
                    'inference_privacy': 4,  # External API calls
                    'score': 4.0
                },
                'fairness': {
                    'group_fairness': 5,  # Difficult to assess
                    'individual_fairness': 4,  # Inconsistent treatment possible
                    'procedural_fairness': 3,  # Opaque procedures
                    'score': 4.0
                },
                'overall_ethical_score': 3.7
            }
        }
        
        print("Ethical Assessment Results:")
        print("-" * 30)
        
        for approach, assessment in ethical_assessment.items():
            print(f"\n{approach}:")
            print(f"  Overall Ethical Score: {assessment['overall_ethical_score']:.1f}/10")
            print(f"  Bias Mitigation Score: {assessment['bias_mitigation']['score']:.1f}/10")
            print(f"  Privacy Protection Score: {assessment['privacy_protection']['score']:.1f}/10")
            print(f"  Fairness Score: {assessment['fairness']['score']:.1f}/10")
            print(f"  Bias Sources:")
            for bias in assessment['bias_sources']:
                print(f"    - {bias}")
        
        # Ethical implications analysis
        print(f"\nEthical Implications Analysis:")
        print(f"Traditional methods show superior ethical characteristics due to transparency")
        print(f"AI methods require careful ethical oversight and bias monitoring")
        print(f"Ethical advantage: Traditional Methods (+{ethical_assessment['Traditional Methods']['overall_ethical_score'] - ethical_assessment['AI Methods']['overall_ethical_score']:.1f} points)")
        
        self.assessment_results['ethical_considerations'] = ethical_assessment
        return ethical_assessment
    
    def assess_implementation_complexity(self):
        """Assess implementation and deployment complexity."""
        print("\n" + "="*60)
        print("IMPLEMENTATION COMPLEXITY ASSESSMENT")
        print("="*60)
        
        implementation_assessment = {
            'Traditional Methods': {
                'setup_complexity': {
                    'initial_setup': 7,  # Moderate setup with libraries
                    'dependency_management': 8,  # Well-established dependencies
                    'configuration': 8,  # Clear configuration options
                    'score': 7.7
                },
                'skill_requirements': {
                    'statistical_knowledge': 8,  # Requires statistical understanding
                    'programming_skills': 6,  # Moderate programming skills
                    'domain_expertise': 7,  # Some domain knowledge helpful
                    'score': 7.0
                },
                'maintenance_effort': {
                    'model_updates': 8,  # Easy to update and retrain
                    'monitoring': 9,  # Easy to monitor performance
                    'debugging': 9,  # Straightforward debugging
                    'score': 8.7
                },
                'resource_requirements': {
                    'computational': 9,  # Low computational requirements
                    'memory': 9,  # Low memory usage
                    'storage': 9,  # Minimal storage needs
                    'score': 9.0
                },
                'overall_complexity_score': 8.1  # Lower complexity (higher score)
            },
            'AI Methods': {
                'setup_complexity': {
                    'initial_setup': 5,  # Complex setup with APIs/models
                    'dependency_management': 6,  # Evolving dependencies
                    'configuration': 4,  # Limited configuration options
                    'score': 5.0
                },
                'skill_requirements': {
                    'statistical_knowledge': 4,  # Less statistical knowledge needed
                    'programming_skills': 7,  # Good programming skills needed
                    'domain_expertise': 5,  # Some AI/ML knowledge helpful
                    'score': 5.3
                },
                'maintenance_effort': {
                    'model_updates': 4,  # Difficult to update proprietary models
                    'monitoring': 5,  # Limited monitoring capabilities
                    'debugging': 3,  # Difficult debugging
                    'score': 4.0
                },
                'resource_requirements': {
                    'computational': 4,  # High computational requirements
                    'memory': 5,  # Significant memory usage
                    'storage': 6,  # Moderate storage needs
                    'score': 5.0
                },
                'overall_complexity_score': 4.8  # Higher complexity (lower score)
            }
        }
        
        print("Implementation Complexity Assessment:")
        print("-" * 40)
        
        for approach, assessment in implementation_assessment.items():
            print(f"\n{approach}:")
            print(f"  Overall Complexity Score: {assessment['overall_complexity_score']:.1f}/10")
            print(f"  Setup Complexity: {assessment['setup_complexity']['score']:.1f}/10")
            print(f"  Skill Requirements: {assessment['skill_requirements']['score']:.1f}/10")
            print(f"  Maintenance Effort: {assessment['maintenance_effort']['score']:.1f}/10")
            print(f"  Resource Requirements: {assessment['resource_requirements']['score']:.1f}/10")
        
        complexity_advantage = implementation_assessment['Traditional Methods']['overall_complexity_score'] - implementation_assessment['AI Methods']['overall_complexity_score']
        print(f"\nImplementation Advantage: Traditional Methods (+{complexity_advantage:.1f} points)")
        print(f"Traditional methods are significantly easier to implement and maintain")
        
        self.assessment_results['implementation_complexity'] = implementation_assessment
        return implementation_assessment
    
    def cost_benefit_analysis(self):
        """Perform comprehensive cost-benefit analysis."""
        print("\n" + "="*60)
        print("COST-BENEFIT ANALYSIS")
        print("="*60)
        
        cost_benefit = {
            'Traditional Methods': {
                'costs': {
                    'development_time': 6,  # Moderate development time
                    'computational_resources': 9,  # Low computational costs
                    'licensing': 10,  # Open source, no licensing costs
                    'training_costs': 5,  # Requires statistical training
                    'maintenance_costs': 8,  # Low maintenance costs
                    'total_cost_score': 7.6  # Lower costs (higher score)
                },
                'benefits': {
                    'performance': 6,  # Moderate performance
                    'interpretability': 9,  # High interpretability
                    'reliability': 8,  # High reliability
                    'flexibility': 7,  # Good flexibility
                    'total_benefit_score': 7.5
                },
                'roi_estimate': 'High - Low costs with solid benefits'
            },
            'AI Methods': {
                'costs': {
                    'development_time': 8,  # Faster development
                    'computational_resources': 4,  # High computational costs
                    'licensing': 6,  # Potential licensing costs
                    'training_costs': 7,  # Less specialized training needed
                    'maintenance_costs': 5,  # Higher maintenance complexity
                    'total_cost_score': 6.0  # Higher costs (lower score)
                },
                'benefits': {
                    'performance': 9,  # High performance
                    'interpretability': 3,  # Low interpretability
                    'reliability': 6,  # Moderate reliability
                    'flexibility': 8,  # High flexibility
                    'total_benefit_score': 6.5
                },
                'roi_estimate': 'Variable - High costs but significant performance gains'
            }
        }
        
        print("Cost-Benefit Analysis Results:")
        print("-" * 30)
        
        for approach, analysis in cost_benefit.items():
            print(f"\n{approach}:")
            print(f"  Total Cost Score: {analysis['costs']['total_cost_score']:.1f}/10 (higher = lower cost)")
            print(f"  Total Benefit Score: {analysis['benefits']['total_benefit_score']:.1f}/10")
            print(f"  ROI Estimate: {analysis['roi_estimate']}")
            
            # Calculate cost-benefit ratio
            cb_ratio = analysis['benefits']['total_benefit_score'] / (11 - analysis['costs']['total_cost_score'])
            print(f"  Cost-Benefit Ratio: {cb_ratio:.2f}")
        
        self.assessment_results['cost_benefit'] = cost_benefit
        return cost_benefit
    
    def create_qualitative_visualizations(self):
        """Create comprehensive qualitative assessment visualizations."""
        print("\n" + "="*60)
        print("CREATING QUALITATIVE ASSESSMENT VISUALIZATIONS")
        print("="*60)
        
        # Create radar chart for multi-dimensional comparison
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Qualitative Assessment Results', fontsize=16, fontweight='bold')
        
        # 1. Interpretability Comparison
        ax1 = axes[0, 0]
        if 'interpretability' in self.assessment_results:
            categories = ['Transparency', 'Explainability', 'Debuggability']
            traditional_scores = [8.0, 7.7, 8.0]  # Average of traditional methods
            ai_scores = [3.0, 4.0, 3.0]  # AI method scores
            
            x = np.arange(len(categories))
            width = 0.35
            
            bars1 = ax1.bar(x - width/2, traditional_scores, width, label='Traditional', color='lightcoral')
            bars2 = ax1.bar(x + width/2, ai_scores, width, label='AI', color='lightgreen')
            
            ax1.set_title('Interpretability Assessment')
            ax1.set_ylabel('Score (1-10)')
            ax1.set_xticks(x)
            ax1.set_xticklabels(categories)
            ax1.legend()
            ax1.set_ylim(0, 10)
            
            # Add value labels
            for bars in [bars1, bars2]:
                for bar in bars:
                    height = bar.get_height()
                    ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                            f'{height:.1f}', ha='center', va='bottom')
        
        # 2. Ethical Considerations
        ax2 = axes[0, 1]
        if 'ethical_considerations' in self.assessment_results:
            ethical_categories = ['Bias Mitigation', 'Privacy Protection', 'Fairness']
            traditional_ethical = [8.7, 8.3, 7.7]
            ai_ethical = [3.0, 4.0, 4.0]
            
            x = np.arange(len(ethical_categories))
            bars1 = ax2.bar(x - width/2, traditional_ethical, width, label='Traditional', color='lightcoral')
            bars2 = ax2.bar(x + width/2, ai_ethical, width, label='AI', color='lightgreen')
            
            ax2.set_title('Ethical Considerations')
            ax2.set_ylabel('Score (1-10)')
            ax2.set_xticks(x)
            ax2.set_xticklabels(ethical_categories, rotation=45)
            ax2.legend()
            ax2.set_ylim(0, 10)
            
            for bars in [bars1, bars2]:
                for bar in bars:
                    height = bar.get_height()
                    ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                            f'{height:.1f}', ha='center', va='bottom')
        
        # 3. Implementation Complexity
        ax3 = axes[1, 0]
        if 'implementation_complexity' in self.assessment_results:
            impl_categories = ['Setup', 'Skills', 'Maintenance', 'Resources']
            traditional_impl = [7.7, 7.0, 8.7, 9.0]
            ai_impl = [5.0, 5.3, 4.0, 5.0]
            
            x = np.arange(len(impl_categories))
            bars1 = ax3.bar(x - width/2, traditional_impl, width, label='Traditional', color='lightcoral')
            bars2 = ax3.bar(x + width/2, ai_impl, width, label='AI', color='lightgreen')
            
            ax3.set_title('Implementation Complexity')
            ax3.set_ylabel('Score (1-10, higher = easier)')
            ax3.set_xticks(x)
            ax3.set_xticklabels(impl_categories)
            ax3.legend()
            ax3.set_ylim(0, 10)
            
            for bars in [bars1, bars2]:
                for bar in bars:
                    height = bar.get_height()
                    ax3.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                            f'{height:.1f}', ha='center', va='bottom')
        
        # 4. Cost-Benefit Summary
        ax4 = axes[1, 1]
        if 'cost_benefit' in self.assessment_results:
            cb_categories = ['Cost Score', 'Benefit Score']
            traditional_cb = [7.6, 7.5]
            ai_cb = [6.0, 6.5]
            
            x = np.arange(len(cb_categories))
            bars1 = ax4.bar(x - width/2, traditional_cb, width, label='Traditional', color='lightcoral')
            bars2 = ax4.bar(x + width/2, ai_cb, width, label='AI', color='lightgreen')
            
            ax4.set_title('Cost-Benefit Analysis')
            ax4.set_ylabel('Score (1-10)')
            ax4.set_xticks(x)
            ax4.set_xticklabels(cb_categories)
            ax4.legend()
            ax4.set_ylim(0, 10)
            
            for bars in [bars1, bars2]:
                for bar in bars:
                    height = bar.get_height()
                    ax4.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                            f'{height:.1f}', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig('qualitative_assessment_results.png', dpi=300, bbox_inches='tight')
        print("✓ Qualitative assessment visualizations saved as 'qualitative_assessment_results.png'")
        
        return fig
    
    def generate_qualitative_report(self):
        """Generate comprehensive qualitative assessment report."""
        print("\n" + "="*60)
        print("GENERATING COMPREHENSIVE QUALITATIVE REPORT")
        print("="*60)
        
        # Run all assessments
        interpretability_results = self.assess_interpretability()
        usability_results = self.assess_usability()
        ethical_results = self.assess_ethical_considerations()
        implementation_results = self.assess_implementation_complexity()
        cost_benefit_results = self.cost_benefit_analysis()
        self.create_qualitative_visualizations()
        
        # Create comprehensive report
        report = {
            'assessment_date': datetime.now().isoformat(),
            'assessment_framework': 'Multi-dimensional Qualitative Evaluation',
            'evaluation_criteria': self.evaluation_criteria,
            'assessment_results': self.assessment_results,
            'overall_analysis': self._generate_overall_analysis(),
            'recommendations': self._generate_qualitative_recommendations()
        }
        
        # Save report
        report_path = "qualitative_assessment_report.json"
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"Qualitative assessment report saved to: {report_path}")
        
        # Print summary
        self._print_qualitative_summary()
        
        return report
    
    def _generate_overall_analysis(self):
        """Generate overall qualitative analysis."""
        return {
            'key_findings': [
                "Traditional methods excel in interpretability and ethical considerations",
                "AI methods provide superior performance but with reduced transparency",
                "Implementation complexity favors traditional approaches",
                "Cost-benefit analysis shows trade-offs between approaches",
                "Context and requirements determine optimal choice"
            ],
            'trade_off_analysis': {
                'performance_vs_interpretability': "AI offers higher performance at the cost of interpretability",
                'cost_vs_capability': "Traditional methods are more cost-effective for basic tasks",
                'complexity_vs_functionality': "AI provides advanced functionality with increased complexity",
                'control_vs_automation': "Traditional methods offer more control, AI provides more automation"
            },
            'contextual_recommendations': {
                'high_stakes_decisions': "Traditional methods preferred for interpretability",
                'performance_critical': "AI methods recommended for maximum performance",
                'resource_constrained': "Traditional methods more suitable",
                'rapid_deployment': "AI methods may offer faster time-to-value"
            }
        }
    
    def _generate_qualitative_recommendations(self):
        """Generate qualitative-based recommendations."""
        return {
            'for_researchers': [
                "Focus on improving AI interpretability and explainability",
                "Develop hybrid approaches combining strengths of both paradigms",
                "Investigate ethical AI frameworks for text mining",
                "Create standardized evaluation frameworks for qualitative assessment"
            ],
            'for_practitioners': [
                "Assess interpretability requirements before method selection",
                "Consider ethical implications in method choice",
                "Evaluate implementation complexity against organizational capabilities",
                "Perform thorough cost-benefit analysis for specific use cases"
            ],
            'for_organizations': [
                "Develop AI governance frameworks for text mining applications",
                "Invest in interpretability tools and training",
                "Consider hybrid deployment strategies",
                "Establish ethical review processes for AI implementations"
            ]
        }
    
    def _print_qualitative_summary(self):
        """Print executive summary of qualitative assessment."""
        print("\nQUALITATIVE ASSESSMENT SUMMARY:")
        print("=" * 40)
        
        print("Interpretability: Traditional methods significantly superior")
        print("Usability: Comparable with different strengths")
        print("Ethical Considerations: Traditional methods more trustworthy")
        print("Implementation: Traditional methods less complex")
        print("Cost-Benefit: Context-dependent trade-offs")
        
        print("\nOverall Recommendation:")
        print("Method selection should be based on specific requirements for")
        print("interpretability, performance, and ethical considerations.")

def main():
    """Main function to run comprehensive qualitative assessment."""
    print("DSCI 6700 Final Project - Qualitative Assessment")
    print("=" * 60)
    
    # Initialize qualitative assessment
    assessor = QualitativeAssessment()
    
    # Generate comprehensive qualitative report
    report = assessor.generate_qualitative_report()
    
    print("\nQualitative assessment complete!")
    print("All assessments, visualizations, and reports have been generated.")

if __name__ == "__main__":
    main()
