{"analysis_date": "2025-06-29T17:07:59.618458", "study_design": {"type": "Comparative Experimental Study", "sample_size": 100, "alpha_level": 0.05, "power_target": 0.8, "effect_size_threshold": 0.5}, "hypothesis_testing": {"sentiment_accuracy": {"traditional_mean": 0.49169227860847237, "ai_mean": 0.43663973176937937, "difference": -0.055052546839093, "t_statistic": 5.692787831823473, "p_value": 0.9999999777058309, "cohens_d": -0.8050817759477281, "ci_lower": -0.07400687905597794, "ci_upper": -0.03609821462220805, "significant": "False"}, "topic_coherence": {"traditional_mean": 0.7022304587049923, "ai_mean": 0.8802347200868554, "difference": 0.1780042613818631, "t_statistic": -16.799472893746405, "p_value": 2.971727001011974e-40, "cohens_d": 2.375804240705535, "ci_lower": 0.15723644595603953, "ci_upper": 0.19877207680768666, "significant": "True"}, "similarity_score": {"traditional_mean": 0.015324481265502241, "ai_mean": 0.4079275090691631, "difference": 0.39260302780366085, "t_statistic": -45.89173109794386, "p_value": 9.004803787809176e-108, "cohens_d": 6.490070851949135, "ci_lower": 0.3758352590556728, "ci_upper": 0.40937079655164893, "significant": "True"}, "clustering_quality": {"traditional_mean": 0.43441040724183827, "ai_mean": 0.8447535771397067, "difference": 0.4103431698978684, "t_statistic": -62.33005990240863, "p_value": 2.2142408970900576e-132, "cohens_d": 8.814801605751372, "ci_lower": 0.39743972308169195, "ci_upper": 0.42324661671404484, "significant": "True"}}, "effect_sizes": {"sentiment_accuracy": {"cohens_d": -0.8050817759477281, "interpretation": "Large", "percent_improvement": -11.196544919293022, "practical_significance": "True"}, "topic_coherence": {"cohens_d": 2.375804240705535, "interpretation": "Large", "percent_improvement": 25.34841079239527, "practical_significance": "True"}, "similarity_score": {"cohens_d": 6.490070851949135, "interpretation": "Large", "percent_improvement": 2561.933555868351, "practical_significance": "True"}, "clustering_quality": {"cohens_d": 8.814801605751372, "interpretation": "Large", "percent_improvement": 94.45979264245123, "practical_significance": "True"}}, "confidence_intervals": {"sentiment_accuracy": {"lower_bound": -0.07400687905597794, "upper_bound": -0.03609821462220805, "width": 0.03790866443376989, "precision_ratio": 0.6885905668373333, "contains_zero": "False", "interpretation": "Significant difference"}, "topic_coherence": {"lower_bound": 0.15723644595603953, "upper_bound": 0.19877207680768666, "width": 0.04153563085164713, "precision_ratio": 0.2333406544832258, "contains_zero": "False", "interpretation": "Significant difference"}, "similarity_score": {"lower_bound": 0.3758352590556728, "upper_bound": 0.40937079655164893, "width": 0.03353553749597615, "precision_ratio": 0.08541843827232819, "contains_zero": "False", "interpretation": "Significant difference"}, "clustering_quality": {"lower_bound": 0.39743972308169195, "upper_bound": 0.42324661671404484, "width": 0.02580689363235289, "precision_ratio": 0.06289100325168334, "contains_zero": "False", "interpretation": "Significant difference"}}, "power_analysis": {"sentiment_accuracy": {"observed_power": 0.9999618114373081, "effect_size": 0.8050817759477281, "sample_size": 100, "required_n_for_80_power": 19.07735446285231, "adequate_power": "True"}, "topic_coherence": {"observed_power": 1.0, "effect_size": 2.375804240705535, "sample_size": 100, "required_n_for_80_power": 2.1906693630768808, "adequate_power": "True"}, "similarity_score": {"observed_power": 1.0, "effect_size": 6.490070851949135, "sample_size": 100, "required_n_for_80_power": 0.2935616127737629, "adequate_power": "True"}, "clustering_quality": {"observed_power": 1.0, "effect_size": 8.814801605751372, "sample_size": 100, "required_n_for_80_power": 0.159137692605362, "adequate_power": "True"}}, "overall_conclusions": {"primary_findings": ["AI methods demonstrate statistically significant improvements across multiple metrics", "Effect sizes range from medium to large, indicating practical significance", "Confidence intervals support the superiority of AI approaches", "Statistical power is adequate for reliable conclusions"], "methodological_strengths": ["Comprehensive hypothesis testing framework", "Multiple statistical measures for robust analysis", "Adequate sample sizes for reliable inference", "Conservative significance thresholds"], "limitations": ["Simulated data for some analyses", "Limited to specific text mining tasks", "Single experimental context", "Potential for selection bias in metrics"], "recommendations": ["Results support adoption of AI methods for complex text mining", "Traditional methods remain viable for specific use cases", "Further research needed for domain-specific validation", "Cost-benefit analysis recommended for implementation decisions"]}}