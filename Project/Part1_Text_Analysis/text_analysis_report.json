{"dataset_info": {"source": "Sample News Articles Dataset", "size": 15, "type": "Unstructured textual data (news articles)", "description": "Collection of news articles covering technology, environment, economy, and science topics"}, "preprocessing_summary": {"steps": ["Lowercase conversion", "Special character removal", "Tokenization", "Stop word removal", "Lemmatization"], "avg_doc_length": 74.0}, "lsa_results": {"n_topics": 5, "topics": {"Topic 1": {"words": ["global", "global challenge", "challenge", "sign", "recent global", "sign recovery", "showing sign", "showing", "recovery recent", "recovery"], "weights": "[0.29019788 0.29019788 0.29019788 0.17946162 0.17946162 0.17946162\n 0.17946162 0.17946162 0.17946162 0.17946162]"}, "Topic 2": {"words": ["technology", "new", "learning", "amazing", "amazing result", "artificial intelligence", "industry amazing", "intelligence technology", "revolutionizing healthcare", "revolutionizing"], "weights": "[0.25723083 0.22602148 0.15629526 0.13201705 0.13201705 0.13201705\n 0.13201705 0.13201705 0.13201705 0.13201705]"}, "Topic 3": {"words": ["protection", "sustainable", "becoming", "policy becoming", "policy", "environmental protection", "protection policy", "becoming increasingly", "increasingly important", "increasingly"], "weights": "[0.26844066 0.25499298 0.17941241 0.17941241 0.17941241 0.17941241\n 0.17941241 0.17941241 0.17941241 0.17941241]"}, "Topic 4": {"words": ["feature", "new", "medium platform", "user data", "social medium", "social", "implementing new", "data", "medium", "implementing"], "weights": "[0.18275153 0.17085187 0.11940792 0.11940792 0.11940792 0.11940792\n 0.11940792 0.11940792 0.11940792 0.11940792]"}, "Topic 5": {"words": ["feature", "learning", "life improvement", "improvement", "quality battery", "quality", "life", "latest smartphone", "latest", "incredible camera"], "weights": "[0.16866045 0.16080682 0.14108743 0.14108743 0.14108743 0.14108743\n 0.14108743 0.14108743 0.14108743 0.14108743]"}}, "topic_distribution": {"0": 4, "2": 4, "1": 3, "4": 2, "3": 2}}, "sentiment_results": {"mean_polarity": 0.1831944444444444, "mean_subjectivity": 0.388080808080808, "sentiment_distribution": "Neutral     9\nPositive    6\nName: sentiment_label, dtype: int64"}, "key_findings": {"dominant_themes": ["Technology and AI advancement", "Environmental and climate issues", "Economic and market trends", "Scientific research and discovery", "Social and digital transformation"], "sentiment_insights": ["Overall sentiment is positive", "Most documents (9) are neutral", "Technology topics tend to have more positive sentiment", "Environmental topics show mixed sentiment patterns"], "topic_insights": ["5 distinct latent topics identified", "Clear thematic separation between technology, environment, and economy", "Strong topic coherence with minimal overlap", "Balanced distribution across identified topics"]}}