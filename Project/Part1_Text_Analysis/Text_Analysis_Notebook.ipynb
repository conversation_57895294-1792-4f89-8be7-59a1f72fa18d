{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# DSCI 6700 Final Project - Part 1: Text Analysis\n", "## Objective: Explore text dataset to uncover latent themes and analyze sentiments\n", "\n", "**Student:** [Your Name]  \n", "**Course:** DSCI 6700 - Text Mining and Unstructured Data  \n", "**Date:** December 2024"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Import Required Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named '<PERSON><PERSON><PERSON><PERSON><PERSON>'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mModuleNotFoundError\u001b[39m                       <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 4\u001b[39m\n\u001b[32m      2\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpd\u001b[39;00m\n\u001b[32m      3\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnumpy\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnp\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m4\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mmatplotlib\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpyplot\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mplt\u001b[39;00m\n\u001b[32m      5\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mseaborn\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01msns\u001b[39;00m\n\u001b[32m      6\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01msklearn\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mfeature_extraction\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mtext\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m TfidfVectorizer\n", "\u001b[31mModuleNotFoundError\u001b[39m: No module named 'matplotlib'"]}], "source": ["# Import all required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.decomposition import TruncatedSVD\n", "from textblob import TextBlob\n", "from wordcloud import WordCloud\n", "import nltk\n", "from nltk.corpus import stopwords\n", "from nltk.tokenize import word_tokenize\n", "from nltk.stem import WordNetLemmatizer\n", "import re\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Download required NLTK data\n", "nltk.download('punkt')\n", "nltk.download('stopwords')\n", "nltk.download('wordnet')\n", "\n", "print(\"✓ All libraries imported successfully\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Dataset Selection and Loading"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create sample dataset (replace with your own dataset)\n", "sample_texts = [\n", "    \"The new artificial intelligence technology is revolutionizing healthcare industry with amazing results.\",\n", "    \"Climate change continues to be a major global challenge affecting millions of people worldwide.\",\n", "    \"The stock market showed positive trends today with technology companies leading the gains.\",\n", "    \"Scientists have discovered a breakthrough in renewable energy that could change everything.\",\n", "    \"The latest smartphone features incredible camera quality and battery life improvements.\",\n", "    \"Environmental protection policies are becoming increasingly important for sustainable development.\",\n", "    \"Machine learning algorithms are being used to predict weather patterns more accurately.\",\n", "    \"The economy is showing signs of recovery after the recent global challenges.\",\n", "    \"New medical research reveals promising treatments for various diseases and conditions.\",\n", "    \"Social media platforms are implementing new privacy features to protect user data.\",\n", "    \"Electric vehicles are gaining popularity as consumers become more environmentally conscious.\",\n", "    \"The education sector is adopting digital technologies to enhance learning experiences.\",\n", "    \"Cybersecurity threats are evolving rapidly requiring advanced protection measures.\",\n", "    \"Space exploration missions are providing valuable insights about our universe.\",\n", "    \"Sustainable agriculture practices are helping farmers increase crop yields efficiently.\"\n", "]\n", "\n", "# Create DataFrame\n", "data = pd.DataFrame({\n", "    'text': sample_texts,\n", "    'id': range(len(sample_texts))\n", "})\n", "\n", "print(f\"Dataset loaded: {len(data)} documents\")\n", "print(f\"Sample text: {data['text'].iloc[0][:100]}...\")\n", "data.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Text Preprocessing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize preprocessing tools\n", "stop_words = set(stopwords.words('english'))\n", "lemmatizer = WordNetLemmatizer()\n", "\n", "def preprocess_text(text):\n", "    \"\"\"Comprehensive text preprocessing pipeline.\"\"\"\n", "    # Convert to lowercase\n", "    text = text.lower()\n", "    \n", "    # Remove special characters and digits\n", "    text = re.sub(r'[^a-zA-Z\\s]', '', text)\n", "    \n", "    # Tokenize\n", "    tokens = word_tokenize(text)\n", "    \n", "    # Remove stop words and lemmatize\n", "    processed_tokens = [\n", "        lemmatizer.lemmatize(token) \n", "        for token in tokens \n", "        if token not in stop_words and len(token) > 2\n", "    ]\n", "    \n", "    return ' '.join(processed_tokens)\n", "\n", "# Apply preprocessing\n", "data['processed_text'] = data['text'].apply(preprocess_text)\n", "\n", "print(\"Preprocessing completed!\")\n", "print(f\"Original: {data['text'].iloc[0]}\")\n", "print(f\"Processed: {data['processed_text'].iloc[0]}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Latent Semantic Analysis (LSA)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create TF-IDF matrix\n", "vectorizer = TfidfVectorizer(\n", "    max_features=1000,\n", "    min_df=1,\n", "    max_df=0.8,\n", "    ngram_range=(1, 2)\n", ")\n", "\n", "tfidf_matrix = vectorizer.fit_transform(data['processed_text'])\n", "feature_names = vectorizer.get_feature_names_out()\n", "\n", "print(f\"TF-IDF matrix shape: {tfidf_matrix.shape}\")\n", "\n", "# Apply LSA using Truncated SVD\n", "n_topics = 5\n", "lsa_model = TruncatedSVD(n_components=n_topics, random_state=42)\n", "lsa_matrix = lsa_model.fit_transform(tfidf_matrix)\n", "\n", "# Extract and display topics\n", "topics = {}\n", "for topic_idx, topic in enumerate(lsa_model.components_):\n", "    top_words_idx = topic.argsort()[-10:][::-1]\n", "    top_words = [feature_names[i] for i in top_words_idx]\n", "    topics[f\"Topic {topic_idx + 1}\"] = top_words\n", "    \n", "    print(f\"\\nTopic {topic_idx + 1}:\")\n", "    for word in top_words[:5]:\n", "        print(f\"  - {word}\")\n", "\n", "# Assign documents to topics\n", "data['dominant_topic'] = np.argmax(lsa_matrix, axis=1)\n", "data['topic_strength'] = np.max(lsa_matrix, axis=1)\n", "\n", "print(f\"\\nTopic distribution:\")\n", "print(data['dominant_topic'].value_counts().sort_index())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Sentiment Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Perform sentiment analysis\n", "sentiments = []\n", "for text in data['text']:\n", "    blob = TextBlob(text)\n", "    sentiment_score = blob.sentiment.polarity\n", "    \n", "    # Classify sentiment\n", "    if sentiment_score > 0.1:\n", "        sentiment_label = 'Positive'\n", "    elif sentiment_score < -0.1:\n", "        sentiment_label = 'Negative'\n", "    else:\n", "        sentiment_label = 'Neutral'\n", "    \n", "    sentiments.append({\n", "        'polarity': sentiment_score,\n", "        'subjectivity': blob.sentiment.subjectivity,\n", "        'label': sentiment_label\n", "    })\n", "\n", "# Add to dataframe\n", "sentiment_df = pd.DataFrame(sentiments)\n", "data['sentiment_polarity'] = sentiment_df['polarity']\n", "data['sentiment_subjectivity'] = sentiment_df['subjectivity']\n", "data['sentiment_label'] = sentiment_df['label']\n", "\n", "# Display results\n", "print(f\"Average Polarity: {data['sentiment_polarity'].mean():.3f}\")\n", "print(f\"Average Subjectivity: {data['sentiment_subjectivity'].mean():.3f}\")\n", "print(f\"\\nSentiment Distribution:\")\n", "print(data['sentiment_label'].value_counts())\n", "\n", "# Sentiment by topic\n", "print(f\"\\nSentiment by Topic:\")\n", "for topic_id in sorted(data['dominant_topic'].unique()):\n", "    topic_data = data[data['dominant_topic'] == topic_id]\n", "    avg_sentiment = topic_data['sentiment_polarity'].mean()\n", "    print(f\"Topic {topic_id + 1}: {avg_sentiment:.3f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Visualizations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create comprehensive visualizations\n", "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "fig.suptitle('Text Analysis Results: LSA and Sentiment Analysis', fontsize=16, fontweight='bold')\n", "\n", "# 1. Word Cloud\n", "ax1 = axes[0, 0]\n", "all_text = ' '.join(data['processed_text'])\n", "wordcloud = WordCloud(width=400, height=300, background_color='white').generate(all_text)\n", "ax1.imshow(wordcloud, interpolation='bilinear')\n", "ax1.set_title('Word Cloud of Processed Text')\n", "ax1.axis('off')\n", "\n", "# 2. Topic Distribution\n", "ax2 = axes[0, 1]\n", "topic_counts = data['dominant_topic'].value_counts().sort_index()\n", "topic_labels = [f'Topic {i+1}' for i in topic_counts.index]\n", "ax2.pie(topic_counts.values, labels=topic_labels, autopct='%1.1f%%')\n", "ax2.set_title('Topic Distribution')\n", "\n", "# 3. Sentiment Distribution\n", "ax3 = axes[0, 2]\n", "sentiment_counts = data['sentiment_label'].value_counts()\n", "colors = ['green' if s == 'Positive' else 'red' if s == 'Negative' else 'gray' for s in sentiment_counts.index]\n", "ax3.bar(sentiment_counts.index, sentiment_counts.values, color=colors, alpha=0.7)\n", "ax3.set_title('Sentiment Distribution')\n", "ax3.set_ylabel('Number of Documents')\n", "\n", "# 4. Sentiment Polarity Distribution\n", "ax4 = axes[1, 0]\n", "ax4.hist(data['sentiment_polarity'], bins=15, alpha=0.7, color='skyblue', edgecolor='black')\n", "ax4.axvline(data['sentiment_polarity'].mean(), color='red', linestyle='--', \n", "           label=f'Mean: {data[\"sentiment_polarity\"].mean():.3f}')\n", "ax4.set_title('Sentiment Polarity Distribution')\n", "ax4.set_xlabel('Polarity Score')\n", "ax4.set_ylabel('Frequency')\n", "ax4.legend()\n", "\n", "# 5. Sentiment by Topic\n", "ax5 = axes[1, 1]\n", "topic_sentiment = data.groupby('dominant_topic')['sentiment_polarity'].mean()\n", "topic_labels = [f'Topic {i+1}' for i in topic_sentiment.index]\n", "bars = ax5.bar(topic_labels, topic_sentiment.values, alpha=0.7)\n", "ax5.set_title('Average Sentiment by Topic')\n", "ax5.set_ylabel('Average Polarity')\n", "ax5.tick_params(axis='x', rotation=45)\n", "\n", "# Color bars based on sentiment\n", "for bar, sentiment in zip(bars, topic_sentiment.values):\n", "    if sentiment > 0:\n", "        bar.set_color('green')\n", "    elif sentiment < 0:\n", "        bar.set_color('red')\n", "    else:\n", "        bar.set_color('gray')\n", "\n", "# 6. Topic Strength Distribution\n", "ax6 = axes[1, 2]\n", "ax6.hist(data['topic_strength'], bins=15, alpha=0.7, color='orange', edgecolor='black')\n", "ax6.set_title('Topic Strength Distribution')\n", "ax6.set_xlabel('Topic Strength')\n", "ax6.set_ylabel('Frequency')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✓ All visualizations generated successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Results Summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display comprehensive results\n", "print(\"=\" * 60)\n", "print(\"TEXT ANALYSIS RESULTS SUMMARY\")\n", "print(\"=\" * 60)\n", "\n", "print(f\"\\nDataset Information:\")\n", "print(f\"- Size: {len(data)} documents\")\n", "print(f\"- Average document length: {data['processed_text'].str.len().mean():.1f} characters\")\n", "\n", "print(f\"\\nLSA Results:\")\n", "print(f\"- Number of topics identified: {n_topics}\")\n", "print(f\"- Topic distribution: {dict(data['dominant_topic'].value_counts().sort_index())}\")\n", "\n", "print(f\"\\nSentiment Analysis Results:\")\n", "print(f\"- Average polarity: {data['sentiment_polarity'].mean():.3f}\")\n", "print(f\"- Average subjectivity: {data['sentiment_subjectivity'].mean():.3f}\")\n", "print(f\"- Sentiment distribution: {dict(data['sentiment_label'].value_counts())}\")\n", "\n", "print(f\"\\nKey Findings:\")\n", "print(f\"- Most common sentiment: {data['sentiment_label'].mode()[0]}\")\n", "print(f\"- Most positive topic: Topic {data.groupby('dominant_topic')['sentiment_polarity'].mean().idxmax() + 1}\")\n", "print(f\"- Overall sentiment trend: {'Positive' if data['sentiment_polarity'].mean() > 0 else 'Negative' if data['sentiment_polarity'].mean() < 0 else 'Neutral'}\")\n", "\n", "print(\"\\n✓ Analysis completed successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Data Export (Optional)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save results to CSV for further analysis\n", "data.to_csv('text_analysis_results.csv', index=False)\n", "print(\"✓ Results saved to 'text_analysis_results.csv'\")\n", "\n", "# Display final dataframe\n", "print(\"\\nFinal dataset with all analysis results:\")\n", "data.head()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}