{"analysis_date": "2025-06-29T16:59:56.080576", "datasets_analyzed": {"news_articles": 5, "product_reviews": 25, "social_media_posts": 30, "academic_abstracts": 15}, "methods_applied": ["TF-IDF Document Representation", "LDA Topic Modeling", "Word2Vec Embeddings", "Traditional Sentiment Analysis", "K-means Document Clustering"], "results": {"tfidf": {"matrix_shape": [5, 272], "feature_count": 272, "top_similarities": [{"doc1_id": 0, "doc2_id": 4, "similarity": 0.0404557450651404, "doc1_title": "AI Revolution in Healthcare: Machine Learning Transforms Medical Diagnosis", "doc2_title": "Virtual Reality Training Programs Show Promise in Professional Education"}, {"doc1_id": 3, "doc2_id": 4, "similarity": 0.024308066441512715, "doc1_title": "Blockchain Technology Faces Scalability Challenges in Enterprise Adoption", "doc2_title": "Virtual Reality Training Programs Show Promise in Professional Education"}, {"doc1_id": 2, "doc2_id": 4, "similarity": 0.023789055082548843, "doc1_title": "5G Network Expansion Accelerates Smart City Development Worldwide", "doc2_title": "Virtual Reality Training Programs Show Promise in Professional Education"}, {"doc1_id": 2, "doc2_id": 3, "similarity": 0.01196175244816476, "doc1_title": "5G Network Expansion Accelerates Smart City Development Worldwide", "doc2_title": "Blockchain Technology Faces Scalability Challenges in Enterprise Adoption"}, {"doc1_id": 1, "doc2_id": 3, "similarity": 0.010848292798725885, "doc1_title": "Quantum Computing Breakthrough: New Processor Achieves Quantum Supremacy", "doc2_title": "Blockchain Technology Faces Scalability Challenges in Enterprise Adoption"}], "avg_similarity": 0.015196537052157486}, "lda": {"num_topics": 5, "topics": [{"topic_id": 0, "words": "0.175*\"learn\" + 0.175*\"machin\" + 0.068*\"quantum\" + 0.047*\"optim\" + 0.026*\"comput\" + 0.026*\"specif\" + 0.026*\"pattern\" + 0.026*\"advantag\" + 0.026*\"explor\" + 0.026*\"complex\""}, {"topic_id": 1, "words": "0.052*\"languag\" + 0.052*\"neural\" + 0.052*\"analysi\" + 0.029*\"question\" + 0.029*\"statist\" + 0.029*\"sentiment\" + 0.029*\"deep\" + 0.029*\"survey\" + 0.029*\"key\" + 0.029*\"architectur\""}, {"topic_id": 2, "words": "0.298*\"cybersecur\" + 0.015*\"analysi\" + 0.015*\"languag\" + 0.015*\"neural\" + 0.014*\"system\" + 0.014*\"translat\" + 0.014*\"learn\" + 0.014*\"includ\" + 0.014*\"tradit\" + 0.014*\"transform\""}, {"topic_id": 3, "words": "0.092*\"languag\" + 0.092*\"process\" + 0.092*\"natur\" + 0.016*\"machin\" + 0.015*\"neural\" + 0.015*\"quantum\" + 0.015*\"learn\" + 0.015*\"analysi\" + 0.015*\"larg\" + 0.015*\"network\""}, {"topic_id": 4, "words": "0.092*\"vision\" + 0.092*\"robot\" + 0.092*\"comput\" + 0.015*\"cybersecur\" + 0.015*\"learn\" + 0.015*\"machin\" + 0.015*\"languag\" + 0.015*\"natur\" + 0.015*\"process\" + 0.015*\"quantum\""}], "doc_topics": [{"doc_id": 0, "title": "Deep Learning Approaches for Natural Language Processing: A Comprehensive Survey", "dominant_topic": 1, "topic_probability": "0.9798914"}, {"doc_id": 1, "title": "Quantum Machine Learning: Bridging Quantum Computing and Artificial Intelligence", "dominant_topic": 0, "topic_probability": "0.98300105"}, {"doc_id": 2, "title": "Sample Academic Paper 3: Advanced Methods in Cybersecurity", "dominant_topic": 0, "topic_probability": "0.83974195"}, {"doc_id": 3, "title": "Sample Academic Paper 4: Advanced Methods in Computer Vision", "dominant_topic": 0, "topic_probability": "0.8397421"}, {"doc_id": 4, "title": "Sample Academic Paper 5: Advanced Methods in Robotics", "dominant_topic": 0, "topic_probability": "0.8397421"}, {"doc_id": 5, "title": "Sample Academic Paper 6: Advanced Methods in Natural Language Processing", "dominant_topic": 3, "topic_probability": "0.8291706"}, {"doc_id": 6, "title": "Sample Academic Paper 7: Advanced Methods in Machine Learning", "dominant_topic": 0, "topic_probability": "0.8397421"}, {"doc_id": 7, "title": "Sample Academic Paper 8: Advanced Methods in Machine Learning", "dominant_topic": 0, "topic_probability": "0.83974195"}, {"doc_id": 8, "title": "Sample Academic Paper 9: Advanced Methods in Natural Language Processing", "dominant_topic": 2, "topic_probability": "0.66462874"}, {"doc_id": 9, "title": "Sample Academic Paper 10: Advanced Methods in Robotics", "dominant_topic": 0, "topic_probability": "0.8397421"}, {"doc_id": 10, "title": "Sample Academic Paper 11: Advanced Methods in Robotics", "dominant_topic": 2, "topic_probability": "0.66462874"}, {"doc_id": 11, "title": "Sample Academic Paper 12: Advanced Methods in Cybersecurity", "dominant_topic": 4, "topic_probability": "0.6385956"}, {"doc_id": 12, "title": "Sample Academic Paper 13: Advanced Methods in Cybersecurity", "dominant_topic": 2, "topic_probability": "0.66462874"}, {"doc_id": 13, "title": "Sample Academic Paper 14: Advanced Methods in Computer Vision", "dominant_topic": 2, "topic_probability": "0.66462874"}, {"doc_id": 14, "title": "Sample Academic Paper 15: Advanced Methods in Machine Learning", "dominant_topic": 4, "topic_probability": "0.76925373"}], "perplexity": -4.317721401400204}, "word2vec": {"vocabulary_size": 93, "vector_size": 100, "similarities": [{"word": "machin", "similar_word": "algorithm", "similarity": 0.4464101195335388}, {"word": "machin", "similar_word": "life", "similarity": 0.4080273509025574}, {"word": "machin", "similar_word": "new", "similarity": 0.39578554034233093}, {"word": "learn", "similar_word": "gener", "similarity": 0.5776339769363403}, {"word": "learn", "similar_word": "algorithm", "similarity": 0.5555300712585449}, {"word": "learn", "similar_word": "signific", "similarity": 0.5320150256156921}], "available_test_words": ["machin", "learn"]}, "sentiment_analysis": {"Naive Bayes": {"accuracy": 0.5, "predictions": ["positive", "neutral", "neutral", "neutral", "neutral", "negative", "neutral", "positive"], "true_labels": ["negative", "negative", "neutral", "neutral", "neutral", "positive", "positive", "positive"]}, "SVM": {"accuracy": 0.5, "predictions": ["positive", "neutral", "neutral", "neutral", "neutral", "negative", "neutral", "positive"], "true_labels": ["negative", "negative", "neutral", "neutral", "neutral", "positive", "positive", "positive"]}}, "clustering": {"n_clusters": 3, "silhouette_score": 0.42790881853835244, "cluster_analysis": [{"cluster_id": 0, "doc_count": 13, "sources": {"academic": "13"}, "doc_indices": [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]}, {"cluster_id": 1, "doc_count": 2, "sources": {"news": "1", "academic": "1"}, "doc_indices": [1, 6]}, {"cluster_id": 2, "doc_count": 5, "sources": {"news": "4", "academic": "1"}, "doc_indices": [0, 2, 3, 4, 5]}], "cluster_labels": [2, 1, 2, 2, 2, 2, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}}