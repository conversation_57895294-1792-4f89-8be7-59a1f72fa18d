"""
Traditional Text Mining Methods Implementation
DSCI 6700 Final Project - Part 2.2

This module implements traditional text mining approaches including:
- TF-IDF for document representation and similarity
- LDA for topic modeling
- Word2Vec for word embeddings
- Sentiment analysis using traditional ML
- Document clustering and classification

Based on course materials and established NLP techniques.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.feature_extraction.text import TfidfVectorizer, CountVectorizer
from sklearn.decomposition import LatentDirichletAllocation, NMF
from sklearn.cluster import KMeans
from sklearn.svm import SVC
from sklearn.naive_bayes import MultinomialNB
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, accuracy_score, silhouette_score
from sklearn.metrics.pairwise import cosine_similarity
from gensim.models import Word2Vec, LdaModel
from gensim.corpora import Dictionary
import nltk
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize
from nltk.stem import PorterStemmer
import re
import json
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Download required NLTK data
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('stopwords')

class TraditionalTextMining:
    """
    Comprehensive implementation of traditional text mining methods
    for comparison with multimodal AI approaches.
    """
    
    def __init__(self, data_dir: str = "../data/Part2_Implementation/data/datasets"):
        """
        Initialize the traditional text mining analyzer.

        Args:
            data_dir (str): Directory containing the datasets
        """
        self.data_dir = data_dir
        self.stop_words = set(stopwords.words('english'))
        self.stemmer = PorterStemmer()
        self.results = {}
        
        # Load datasets
        self.load_datasets()
        
    def load_datasets(self):
        """Load all datasets for analysis."""
        print("Loading datasets...")
        
        # Load news articles
        news_path = os.path.join(self.data_dir, "news_articles.csv")
        self.news_df = pd.read_csv(news_path)
        
        # Load product reviews
        reviews_path = os.path.join(self.data_dir, "product_reviews.csv")
        self.reviews_df = pd.read_csv(reviews_path)
        
        # Load social media posts
        social_path = os.path.join(self.data_dir, "social_media_posts.csv")
        self.social_df = pd.read_csv(social_path)
        
        # Load academic abstracts
        abstracts_path = os.path.join(self.data_dir, "academic_abstracts.csv")
        self.abstracts_df = pd.read_csv(abstracts_path)
        
        print(f"Loaded datasets:")
        print(f"- News articles: {len(self.news_df)}")
        print(f"- Product reviews: {len(self.reviews_df)}")
        print(f"- Social media posts: {len(self.social_df)}")
        print(f"- Academic abstracts: {len(self.abstracts_df)}")
        
    def preprocess_text(self, text: str) -> str:
        """
        Preprocess text using traditional NLP techniques.
        
        Args:
            text (str): Raw text to preprocess
            
        Returns:
            str: Preprocessed text
        """
        if pd.isna(text):
            return ""
        
        # Convert to lowercase
        text = text.lower()
        
        # Remove special characters and digits
        text = re.sub(r'[^a-zA-Z\s]', '', text)
        
        # Tokenize
        tokens = word_tokenize(text)
        
        # Remove stopwords and stem
        tokens = [self.stemmer.stem(token) for token in tokens 
                 if token not in self.stop_words and len(token) > 2]
        
        return ' '.join(tokens)
    
    def tfidf_analysis(self):
        """
        Perform TF-IDF analysis on news articles.
        Based on course material: Python_code_TF_IDF.txt
        """
        print("\n" + "="*50)
        print("TF-IDF ANALYSIS")
        print("="*50)
        
        # Prepare documents
        documents = self.news_df['content'].fillna('').tolist()
        preprocessed_docs = [self.preprocess_text(doc) for doc in documents]
        
        # Create TF-IDF vectorizer
        vectorizer = TfidfVectorizer(
            max_features=1000,
            min_df=1,
            max_df=0.8,
            ngram_range=(1, 2)
        )
        
        # Fit and transform documents
        tfidf_matrix = vectorizer.fit_transform(preprocessed_docs)
        feature_names = vectorizer.get_feature_names_out()
        
        print(f"TF-IDF Matrix Shape: {tfidf_matrix.shape}")
        print(f"Number of features: {len(feature_names)}")
        
        # Get top terms for each document
        tfidf_dense = tfidf_matrix.todense()
        
        # Document similarity analysis
        similarity_matrix = cosine_similarity(tfidf_matrix)
        
        # Find most similar document pairs
        similarity_results = []
        for i in range(len(documents)):
            for j in range(i+1, len(documents)):
                similarity_results.append({
                    'doc1_id': i,
                    'doc2_id': j,
                    'similarity': similarity_matrix[i][j],
                    'doc1_title': self.news_df.iloc[i]['title'],
                    'doc2_title': self.news_df.iloc[j]['title']
                })
        
        # Sort by similarity
        similarity_results.sort(key=lambda x: x['similarity'], reverse=True)
        
        print("\nTop 3 Most Similar Document Pairs:")
        for i, result in enumerate(similarity_results[:3]):
            print(f"{i+1}. Similarity: {result['similarity']:.3f}")
            print(f"   Doc 1: {result['doc1_title']}")
            print(f"   Doc 2: {result['doc2_title']}")
        
        # Store results
        self.results['tfidf'] = {
            'matrix_shape': tfidf_matrix.shape,
            'feature_count': len(feature_names),
            'top_similarities': similarity_results[:5],
            'avg_similarity': np.mean([r['similarity'] for r in similarity_results])
        }
        
        return tfidf_matrix, vectorizer, similarity_matrix
    
    def lda_topic_modeling(self):
        """
        Perform LDA topic modeling on academic abstracts.
        Based on course material: Python_code_LDA.txt
        """
        print("\n" + "="*50)
        print("LDA TOPIC MODELING")
        print("="*50)
        
        # Prepare documents
        documents = self.abstracts_df['abstract'].fillna('').tolist()
        preprocessed_docs = [self.preprocess_text(doc).split() for doc in documents]
        
        # Create dictionary and corpus for Gensim
        dictionary = Dictionary(preprocessed_docs)
        dictionary.filter_extremes(no_below=1, no_above=0.8)
        corpus = [dictionary.doc2bow(doc) for doc in preprocessed_docs]
        
        print(f"Dictionary size: {len(dictionary)}")
        print(f"Corpus size: {len(corpus)}")
        
        # Train LDA model
        num_topics = 5
        lda_model = LdaModel(
            corpus=corpus,
            id2word=dictionary,
            num_topics=num_topics,
            random_state=42,
            passes=10,
            alpha='auto',
            per_word_topics=True
        )
        
        # Display topics
        print(f"\nDiscovered {num_topics} topics:")
        topics = []
        for idx, topic in lda_model.print_topics(-1):
            print(f"Topic {idx}: {topic}")
            topics.append({
                'topic_id': idx,
                'words': topic
            })
        
        # Document-topic distribution
        doc_topics = []
        for i, doc in enumerate(corpus):
            topic_dist = lda_model.get_document_topics(doc)
            dominant_topic = max(topic_dist, key=lambda x: x[1])
            doc_topics.append({
                'doc_id': i,
                'title': self.abstracts_df.iloc[i]['title'],
                'dominant_topic': dominant_topic[0],
                'topic_probability': dominant_topic[1]
            })
        
        print(f"\nDocument-Topic Assignments (Top 3):")
        for i, doc_topic in enumerate(doc_topics[:3]):
            print(f"Doc {i+1}: Topic {doc_topic['dominant_topic']} "
                  f"(prob: {doc_topic['topic_probability']:.3f})")
            print(f"Title: {doc_topic['title']}")
        
        # Store results
        self.results['lda'] = {
            'num_topics': num_topics,
            'topics': topics,
            'doc_topics': doc_topics,
            'perplexity': lda_model.log_perplexity(corpus)
        }
        
        return lda_model, dictionary, corpus
    
    def word2vec_analysis(self):
        """
        Perform Word2Vec analysis for word embeddings.
        Based on course material: Python_code_Word2Vec.txt
        """
        print("\n" + "="*50)
        print("WORD2VEC ANALYSIS")
        print("="*50)
        
        # Prepare sentences from all text data
        all_texts = []
        all_texts.extend(self.news_df['content'].fillna('').tolist())
        all_texts.extend(self.reviews_df['review'].fillna('').tolist())
        all_texts.extend(self.social_df['content'].fillna('').tolist())
        all_texts.extend(self.abstracts_df['abstract'].fillna('').tolist())
        
        # Preprocess and tokenize
        sentences = []
        for text in all_texts:
            preprocessed = self.preprocess_text(text)
            if preprocessed:
                sentences.append(preprocessed.split())
        
        print(f"Training Word2Vec on {len(sentences)} sentences")
        
        # Train Word2Vec model
        w2v_model = Word2Vec(
            sentences=sentences,
            vector_size=100,
            window=5,
            min_count=2,
            workers=4,
            sg=1  # Skip-gram
        )
        
        print(f"Vocabulary size: {len(w2v_model.wv.key_to_index)}")
        
        # Test word similarities
        test_words = ['technologi', 'artifici', 'machin', 'learn', 'data']
        available_words = [word for word in test_words if word in w2v_model.wv.key_to_index]
        
        print(f"\nWord Similarity Analysis:")
        similarities = []
        for word in available_words:
            try:
                similar_words = w2v_model.wv.most_similar(word, topn=3)
                print(f"Words similar to '{word}':")
                for sim_word, similarity in similar_words:
                    print(f"  {sim_word}: {similarity:.3f}")
                    similarities.append({
                        'word': word,
                        'similar_word': sim_word,
                        'similarity': similarity
                    })
            except KeyError:
                continue
        
        # Store results
        self.results['word2vec'] = {
            'vocabulary_size': len(w2v_model.wv.key_to_index),
            'vector_size': w2v_model.wv.vector_size,
            'similarities': similarities,
            'available_test_words': available_words
        }
        
        return w2v_model
    
    def sentiment_analysis_traditional(self):
        """
        Perform sentiment analysis using traditional ML methods.
        """
        print("\n" + "="*50)
        print("TRADITIONAL SENTIMENT ANALYSIS")
        print("="*50)
        
        # Prepare data from product reviews
        texts = self.reviews_df['review'].fillna('').tolist()
        labels = self.reviews_df['sentiment'].tolist()
        
        # Preprocess texts
        preprocessed_texts = [self.preprocess_text(text) for text in texts]
        
        # Create TF-IDF features
        vectorizer = TfidfVectorizer(max_features=1000, min_df=1, max_df=0.8)
        X = vectorizer.fit_transform(preprocessed_texts)
        y = labels
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.3, random_state=42, stratify=y
        )
        
        print(f"Training set size: {X_train.shape[0]}")
        print(f"Test set size: {X_test.shape[0]}")
        
        # Train classifiers
        classifiers = {
            'Naive Bayes': MultinomialNB(),
            'SVM': SVC(kernel='linear', random_state=42)
        }
        
        results = {}
        for name, clf in classifiers.items():
            print(f"\nTraining {name}...")
            clf.fit(X_train, y_train)
            y_pred = clf.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)
            
            print(f"{name} Accuracy: {accuracy:.3f}")
            print(f"Classification Report:")
            print(classification_report(y_test, y_pred))
            
            results[name] = {
                'accuracy': accuracy,
                'predictions': y_pred.tolist(),
                'true_labels': y_test
            }
        
        # Store results
        self.results['sentiment_analysis'] = results
        
        return results
    
    def document_clustering(self):
        """
        Perform document clustering using K-means.
        """
        print("\n" + "="*50)
        print("DOCUMENT CLUSTERING")
        print("="*50)
        
        # Combine all documents
        all_docs = []
        doc_sources = []
        
        # Add news articles
        for _, row in self.news_df.iterrows():
            all_docs.append(row['content'])
            doc_sources.append('news')
        
        # Add abstracts
        for _, row in self.abstracts_df.iterrows():
            all_docs.append(row['abstract'])
            doc_sources.append('academic')
        
        # Preprocess
        preprocessed_docs = [self.preprocess_text(doc) for doc in all_docs]
        
        # Create TF-IDF features
        vectorizer = TfidfVectorizer(max_features=500, min_df=1, max_df=0.8)
        X = vectorizer.fit_transform(preprocessed_docs)
        
        # Perform clustering
        n_clusters = 3
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        cluster_labels = kmeans.fit_predict(X)
        
        # Calculate silhouette score
        silhouette_avg = silhouette_score(X, cluster_labels)
        
        print(f"Number of clusters: {n_clusters}")
        print(f"Silhouette Score: {silhouette_avg:.3f}")
        
        # Analyze clusters
        cluster_analysis = []
        for i in range(n_clusters):
            cluster_docs = [j for j, label in enumerate(cluster_labels) if label == i]
            cluster_sources = [doc_sources[j] for j in cluster_docs]
            
            print(f"\nCluster {i}: {len(cluster_docs)} documents")
            print(f"Sources: {dict(pd.Series(cluster_sources).value_counts())}")
            
            cluster_analysis.append({
                'cluster_id': i,
                'doc_count': len(cluster_docs),
                'sources': dict(pd.Series(cluster_sources).value_counts()),
                'doc_indices': cluster_docs
            })
        
        # Store results
        self.results['clustering'] = {
            'n_clusters': n_clusters,
            'silhouette_score': silhouette_avg,
            'cluster_analysis': cluster_analysis,
            'cluster_labels': cluster_labels.tolist()
        }
        
        return cluster_labels, silhouette_avg
    
    def generate_comprehensive_report(self):
        """Generate comprehensive analysis report."""
        print("\n" + "="*60)
        print("TRADITIONAL TEXT MINING - COMPREHENSIVE REPORT")
        print("="*60)
        
        report = {
            'analysis_date': datetime.now().isoformat(),
            'datasets_analyzed': {
                'news_articles': len(self.news_df),
                'product_reviews': len(self.reviews_df),
                'social_media_posts': len(self.social_df),
                'academic_abstracts': len(self.abstracts_df)
            },
            'methods_applied': [
                'TF-IDF Document Representation',
                'LDA Topic Modeling',
                'Word2Vec Embeddings',
                'Traditional Sentiment Analysis',
                'K-means Document Clustering'
            ],
            'results': self.results
        }
        
        # Save report
        report_path = "traditional_methods_report.json"
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"Comprehensive report saved to: {report_path}")
        
        # Print summary
        print("\nANALYSIS SUMMARY:")
        print("-" * 40)
        
        if 'tfidf' in self.results:
            print(f"TF-IDF Analysis:")
            print(f"  - Matrix shape: {self.results['tfidf']['matrix_shape']}")
            print(f"  - Average similarity: {self.results['tfidf']['avg_similarity']:.3f}")
        
        if 'lda' in self.results:
            print(f"LDA Topic Modeling:")
            print(f"  - Topics discovered: {self.results['lda']['num_topics']}")
            print(f"  - Model perplexity: {self.results['lda']['perplexity']:.3f}")
        
        if 'word2vec' in self.results:
            print(f"Word2Vec Analysis:")
            print(f"  - Vocabulary size: {self.results['word2vec']['vocabulary_size']}")
            print(f"  - Vector dimensions: {self.results['word2vec']['vector_size']}")
        
        if 'sentiment_analysis' in self.results:
            print(f"Sentiment Analysis:")
            for method, result in self.results['sentiment_analysis'].items():
                print(f"  - {method} accuracy: {result['accuracy']:.3f}")
        
        if 'clustering' in self.results:
            print(f"Document Clustering:")
            print(f"  - Clusters: {self.results['clustering']['n_clusters']}")
            print(f"  - Silhouette score: {self.results['clustering']['silhouette_score']:.3f}")
        
        return report

def main():
    """Main function to run all traditional text mining analyses."""
    print("DSCI 6700 Final Project - Traditional Text Mining Analysis")
    print("=" * 60)
    
    # Initialize analyzer
    analyzer = TraditionalTextMining()
    
    # Run all analyses
    print("\nRunning comprehensive traditional text mining analysis...")
    
    # 1. TF-IDF Analysis
    analyzer.tfidf_analysis()
    
    # 2. LDA Topic Modeling
    analyzer.lda_topic_modeling()
    
    # 3. Word2Vec Analysis
    analyzer.word2vec_analysis()
    
    # 4. Sentiment Analysis
    analyzer.sentiment_analysis_traditional()
    
    # 5. Document Clustering
    analyzer.document_clustering()
    
    # 6. Generate Report
    report = analyzer.generate_comprehensive_report()
    
    print("\nTraditional text mining analysis complete!")
    print("Results saved for comparison with multimodal AI approaches.")

if __name__ == "__main__":
    main()
