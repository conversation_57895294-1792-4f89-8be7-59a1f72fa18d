"""
TF-IDF Analysis Implementation for DSCI 6700 Final Project
=========================================================

This module implements Term Frequency-Inverse Document Frequency (TF-IDF) analysis
for text mining and document similarity tasks. It demonstrates traditional text
mining approaches as part of the comparative analysis with multimodal AI methods.

Author: [Student Name]
Course: DSCI 6700 - Text Mining and Unstructured Data
Date: [Current Date]
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA
import nltk
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize
from nltk.stem import PorterStemmer
import re
import json
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Download required NLTK data
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('stopwords')

class TFIDFAnalyzer:
    """
    A comprehensive TF-IDF analyzer for text mining tasks.

    This class provides methods for:
    - Text preprocessing and cleaning
    - TF-IDF vectorization
    - Document similarity analysis
    - Keyword extraction
    - Document clustering
    - Visualization of results
    """

    def __init__(self, max_features=1000, min_df=2, max_df=0.8, ngram_range=(1, 2)):
        """
        Initialize the TF-IDF analyzer with specified parameters.

        Args:
            max_features (int): Maximum number of features to extract
            min_df (int): Minimum document frequency for terms
            max_df (float): Maximum document frequency for terms
            ngram_range (tuple): Range of n-grams to consider
        """
        self.max_features = max_features
        self.min_df = min_df
        self.max_df = max_df
        self.ngram_range = ngram_range

        # Initialize components
        self.vectorizer = None
        self.tfidf_matrix = None
        self.feature_names = None
        self.documents = None
        self.processed_documents = None

        # Initialize text preprocessing tools
        self.stemmer = PorterStemmer()
        self.stop_words = set(stopwords.words('english'))

        print(f"TF-IDF Analyzer initialized with:")
        print(f"  - Max features: {max_features}")
        print(f"  - Min document frequency: {min_df}")
        print(f"  - Max document frequency: {max_df}")
        print(f"  - N-gram range: {ngram_range}")

    def preprocess_text(self, text):
        """
        Preprocess a single text document.

        Args:
            text (str): Raw text to preprocess

        Returns:
            str: Cleaned and preprocessed text
        """
        # Convert to lowercase
        text = text.lower()

        # Remove special characters and digits
        text = re.sub(r'[^a-zA-Z\s]', '', text)

        # Tokenize
        tokens = word_tokenize(text)

        # Remove stopwords and apply stemming
        processed_tokens = [
            self.stemmer.stem(token)
            for token in tokens
            if token not in self.stop_words and len(token) > 2
        ]

        return ' '.join(processed_tokens)

    def fit_transform(self, documents):
        """
        Fit the TF-IDF vectorizer and transform documents.

        Args:
            documents (list): List of text documents

        Returns:
            scipy.sparse.matrix: TF-IDF matrix
        """
        print(f"\nProcessing {len(documents)} documents...")

        # Store original documents
        self.documents = documents

        # Preprocess all documents
        print("Preprocessing documents...")
        self.processed_documents = [self.preprocess_text(doc) for doc in documents]

        # Initialize and fit TF-IDF vectorizer
        print("Fitting TF-IDF vectorizer...")
        self.vectorizer = TfidfVectorizer(
            max_features=self.max_features,
            min_df=self.min_df,
            max_df=self.max_df,
            ngram_range=self.ngram_range,
            stop_words='english'
        )

        # Transform documents to TF-IDF matrix
        self.tfidf_matrix = self.vectorizer.fit_transform(self.processed_documents)
        self.feature_names = self.vectorizer.get_feature_names_out()

        print(f"TF-IDF matrix shape: {self.tfidf_matrix.shape}")
        print(f"Number of features: {len(self.feature_names)}")

        return self.tfidf_matrix

    def get_top_keywords(self, doc_index, top_k=10):
        """
        Extract top keywords from a specific document.

        Args:
            doc_index (int): Index of the document
            top_k (int): Number of top keywords to return

        Returns:
            list: List of tuples (keyword, tfidf_score)
        """
        if self.tfidf_matrix is None:
            raise ValueError("TF-IDF matrix not computed. Call fit_transform first.")

        # Get TF-IDF scores for the document
        doc_tfidf = self.tfidf_matrix[doc_index].toarray().flatten()

        # Get indices of top keywords
        top_indices = doc_tfidf.argsort()[-top_k:][::-1]

        # Return keywords with their scores
        top_keywords = [
            (self.feature_names[i], doc_tfidf[i])
            for i in top_indices if doc_tfidf[i] > 0
        ]

        return top_keywords

    def compute_document_similarity(self):
        """
        Compute cosine similarity between all document pairs.

        Returns:
            numpy.ndarray: Similarity matrix
        """
        if self.tfidf_matrix is None:
            raise ValueError("TF-IDF matrix not computed. Call fit_transform first.")

        print("Computing document similarity matrix...")
        similarity_matrix = cosine_similarity(self.tfidf_matrix)

        return similarity_matrix

    def find_similar_documents(self, doc_index, top_k=5):
        """
        Find documents most similar to a given document.

        Args:
            doc_index (int): Index of the reference document
            top_k (int): Number of similar documents to return

        Returns:
            list: List of tuples (doc_index, similarity_score)
        """
        similarity_matrix = self.compute_document_similarity()

        # Get similarity scores for the reference document
        similarities = similarity_matrix[doc_index]

        # Get indices of most similar documents (excluding self)
        similar_indices = similarities.argsort()[-top_k-1:-1][::-1]

        # Return similar documents with their scores
        similar_docs = [
            (i, similarities[i])
            for i in similar_indices if i != doc_index
        ]

        return similar_docs

    def cluster_documents(self, n_clusters=5, random_state=42):
        """
        Cluster documents using K-means clustering.

        Args:
            n_clusters (int): Number of clusters
            random_state (int): Random state for reproducibility

        Returns:
            numpy.ndarray: Cluster labels for each document
        """
        if self.tfidf_matrix is None:
            raise ValueError("TF-IDF matrix not computed. Call fit_transform first.")

        print(f"Clustering documents into {n_clusters} clusters...")

        # Apply K-means clustering
        kmeans = KMeans(n_clusters=n_clusters, random_state=random_state, n_init=10)
        cluster_labels = kmeans.fit_predict(self.tfidf_matrix)

        return cluster_labels

    def visualize_tfidf_heatmap(self, top_terms=20, top_docs=10, save_path=None):
        """
        Create a heatmap visualization of TF-IDF scores.

        Args:
            top_terms (int): Number of top terms to display
            top_docs (int): Number of documents to display
            save_path (str): Path to save the plot
        """
        if self.tfidf_matrix is None:
            raise ValueError("TF-IDF matrix not computed. Call fit_transform first.")

        # Convert to dense array for visualization
        tfidf_dense = self.tfidf_matrix[:top_docs, :top_terms].toarray()

        # Create DataFrame for better visualization
        df_tfidf = pd.DataFrame(
            tfidf_dense,
            columns=self.feature_names[:top_terms],
            index=[f"Doc {i}" for i in range(top_docs)]
        )

        # Create heatmap
        plt.figure(figsize=(15, 8))
        sns.heatmap(df_tfidf, annot=True, fmt='.3f', cmap='YlOrRd', cbar=True)
        plt.title(f'TF-IDF Heatmap: Top {top_terms} Terms across {top_docs} Documents')
        plt.xlabel('Terms')
        plt.ylabel('Documents')
        plt.xticks(rotation=45, ha='right')
        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Heatmap saved to: {save_path}")

        plt.show()

    def visualize_document_clusters(self, cluster_labels, save_path=None):
        """
        Visualize document clusters using PCA.

        Args:
            cluster_labels (numpy.ndarray): Cluster labels for each document
            save_path (str): Path to save the plot
        """
        if self.tfidf_matrix is None:
            raise ValueError("TF-IDF matrix not computed. Call fit_transform first.")

        # Apply PCA for dimensionality reduction
        pca = PCA(n_components=2, random_state=42)
        tfidf_2d = pca.fit_transform(self.tfidf_matrix.toarray())

        # Create scatter plot
        plt.figure(figsize=(12, 8))
        scatter = plt.scatter(tfidf_2d[:, 0], tfidf_2d[:, 1], c=cluster_labels, cmap='viridis', alpha=0.7)
        plt.colorbar(scatter)
        plt.title('Document Clusters Visualization (PCA)')
        plt.xlabel(f'First Principal Component (Explained Variance: {pca.explained_variance_ratio_[0]:.2%})')
        plt.ylabel(f'Second Principal Component (Explained Variance: {pca.explained_variance_ratio_[1]:.2%})')

        # Add cluster centers
        unique_labels = np.unique(cluster_labels)
        for label in unique_labels:
            cluster_points = tfidf_2d[cluster_labels == label]
            center = cluster_points.mean(axis=0)
            plt.scatter(center[0], center[1], c='red', marker='x', s=200, linewidths=3)

        plt.grid(True, alpha=0.3)
        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Cluster visualization saved to: {save_path}")

        plt.show()

    def save_results(self, output_dir):
        """
        Save analysis results to files.

        Args:
            output_dir (str): Directory to save results
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        # Save TF-IDF matrix
        tfidf_df = pd.DataFrame(
            self.tfidf_matrix.toarray(),
            columns=self.feature_names
        )
        tfidf_df.to_csv(output_path / 'tfidf_matrix.csv', index=False)

        # Save feature names
        with open(output_path / 'feature_names.json', 'w') as f:
            json.dump(self.feature_names.tolist(), f, indent=2)

        # Save document similarity matrix
        similarity_matrix = self.compute_document_similarity()
        similarity_df = pd.DataFrame(similarity_matrix)
        similarity_df.to_csv(output_path / 'document_similarity.csv', index=False)

        print(f"Results saved to: {output_path}")


def load_sample_data():
    """
    Load sample data for demonstration.

    Returns:
        list: List of sample documents
    """
    # Sample documents for demonstration
    sample_documents = [
        "Artificial intelligence is transforming the world of technology and business.",
        "Machine learning algorithms can analyze large datasets to find patterns.",
        "Natural language processing enables computers to understand human language.",
        "Deep learning neural networks are inspired by the human brain structure.",
        "Data science combines statistics, programming, and domain expertise.",
        "Text mining extracts useful information from unstructured text data.",
        "Computer vision allows machines to interpret and analyze visual information.",
        "Big data analytics helps organizations make data-driven decisions.",
        "Cloud computing provides scalable and flexible computing resources.",
        "Cybersecurity protects digital systems from threats and attacks.",
        "The Internet of Things connects everyday devices to the internet.",
        "Blockchain technology ensures secure and transparent transactions.",
        "Quantum computing promises to solve complex computational problems.",
        "Robotics automation is revolutionizing manufacturing and service industries.",
        "Virtual reality creates immersive digital experiences for users."
    ]

    return sample_documents


def main():
    """
    Main function to demonstrate TF-IDF analysis.
    """
    print("=" * 60)
    print("TF-IDF Analysis for DSCI 6700 Final Project")
    print("=" * 60)

    # Load sample data
    print("\n1. Loading sample data...")
    documents = load_sample_data()
    print(f"Loaded {len(documents)} sample documents")

    # Initialize TF-IDF analyzer
    print("\n2. Initializing TF-IDF analyzer...")
    analyzer = TFIDFAnalyzer(max_features=100, min_df=1, max_df=0.8, ngram_range=(1, 2))

    # Fit and transform documents
    print("\n3. Computing TF-IDF matrix...")
    tfidf_matrix = analyzer.fit_transform(documents)

    # Extract top keywords for first document
    print("\n4. Extracting top keywords...")
    top_keywords = analyzer.get_top_keywords(0, top_k=10)
    print(f"Top keywords for Document 0:")
    for keyword, score in top_keywords:
        print(f"  {keyword}: {score:.4f}")

    # Find similar documents
    print("\n5. Finding similar documents...")
    similar_docs = analyzer.find_similar_documents(0, top_k=3)
    print(f"Documents most similar to Document 0:")
    for doc_idx, similarity in similar_docs:
        print(f"  Document {doc_idx}: {similarity:.4f}")
        print(f"    Content: {documents[doc_idx][:80]}...")

    # Cluster documents
    print("\n6. Clustering documents...")
    cluster_labels = analyzer.cluster_documents(n_clusters=3)

    # Display cluster information
    for cluster_id in np.unique(cluster_labels):
        docs_in_cluster = np.where(cluster_labels == cluster_id)[0]
        print(f"\nCluster {cluster_id} ({len(docs_in_cluster)} documents):")
        for doc_idx in docs_in_cluster[:3]:  # Show first 3 documents
            print(f"  Doc {doc_idx}: {documents[doc_idx][:60]}...")

    # Create visualizations
    print("\n7. Creating visualizations...")

    # Create output directory
    output_dir = "../results/traditional_results/tfidf"
    Path(output_dir).mkdir(parents=True, exist_ok=True)

    # Generate heatmap
    analyzer.visualize_tfidf_heatmap(
        top_terms=15,
        top_docs=10,
        save_path=f"{output_dir}/tfidf_heatmap.png"
    )

    # Generate cluster visualization
    analyzer.visualize_document_clusters(
        cluster_labels,
        save_path=f"{output_dir}/document_clusters.png"
    )

    # Save results
    print("\n8. Saving results...")
    analyzer.save_results(output_dir)

    print("\n" + "=" * 60)
    print("TF-IDF Analysis Complete!")
    print("=" * 60)

    return analyzer, tfidf_matrix, cluster_labels


if __name__ == "__main__":
    # Run the main analysis
    analyzer, tfidf_matrix, cluster_labels = main()

    # Additional analysis can be performed here
    print("\nAnalysis completed successfully!")
    print("Check the results directory for saved outputs and visualizations.")