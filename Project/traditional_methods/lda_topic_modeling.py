"""
LDA Topic Modeling Implementation for DSCI 6700 Final Project
============================================================

This module implements Latent Dirichlet Allocation (LDA) for topic modeling
and document analysis. It demonstrates traditional text mining approaches
as part of the comparative analysis with multimodal AI methods.

Author: [Student Name]
Course: DSCI 6700 - Text Mining and Unstructured Data
Date: [Current Date]
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from gensim import corpora, models
from gensim.models import LdaModel, CoherenceModel
from gensim.parsing.preprocessing import STOPWORDS
import nltk
from nltk.tokenize import word_tokenize
from nltk.corpus import stopwords
from nltk.stem import WordNetLemmatizer
import re
import json
from pathlib import Path
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

# Download required NLTK data
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('stopwords')

try:
    nltk.data.find('corpora/wordnet')
except LookupError:
    nltk.download('wordnet')

class LDATopicModeler:
    """
    A comprehensive LDA topic modeling analyzer.

    This class provides methods for:
    - Text preprocessing and tokenization
    - Dictionary and corpus creation
    - LDA model training and optimization
    - Topic analysis and interpretation
    - Document-topic distribution analysis
    - Visualization of results
    """

    def __init__(self, num_topics=5, alpha='auto', beta='auto', random_state=42):
        """
        Initialize the LDA topic modeler.

        Args:
            num_topics (int): Number of topics to extract
            alpha (str or float): Document-topic density parameter
            beta (str or float): Topic-word density parameter
            random_state (int): Random state for reproducibility
        """
        self.num_topics = num_topics
        self.alpha = alpha
        self.beta = beta
        self.random_state = random_state

        # Initialize components
        self.documents = None
        self.processed_documents = None
        self.dictionary = None
        self.corpus = None
        self.lda_model = None
        self.coherence_score = None

        # Initialize text preprocessing tools
        self.lemmatizer = WordNetLemmatizer()
        self.stop_words = set(stopwords.words('english')).union(STOPWORDS)

        print(f"LDA Topic Modeler initialized with:")
        print(f"  - Number of topics: {num_topics}")
        print(f"  - Alpha (document-topic density): {alpha}")
        print(f"  - Beta (topic-word density): {beta}")
        print(f"  - Random state: {random_state}")

    def preprocess_text(self, text):
        """
        Preprocess a single text document for LDA.

        Args:
            text (str): Raw text to preprocess

        Returns:
            list: List of preprocessed tokens
        """
        # Convert to lowercase and remove special characters
        text = re.sub(r'[^a-zA-Z\s]', '', text.lower())

        # Tokenize
        tokens = word_tokenize(text)

        # Remove stopwords, short words, and apply lemmatization
        processed_tokens = [
            self.lemmatizer.lemmatize(token)
            for token in tokens
            if token not in self.stop_words and len(token) > 3
        ]

        return processed_tokens

    def prepare_corpus(self, documents, min_count=2, max_freq=0.8):
        """
        Prepare the corpus for LDA training.

        Args:
            documents (list): List of text documents
            min_count (int): Minimum word frequency to include in dictionary
            max_freq (float): Maximum document frequency for words

        Returns:
            tuple: (dictionary, corpus)
        """
        print(f"\nPreparing corpus from {len(documents)} documents...")

        # Store original documents
        self.documents = documents

        # Preprocess all documents
        print("Preprocessing documents...")
        self.processed_documents = [self.preprocess_text(doc) for doc in documents]

        # Remove empty documents
        self.processed_documents = [doc for doc in self.processed_documents if doc]

        # Create dictionary
        print("Creating dictionary...")
        self.dictionary = corpora.Dictionary(self.processed_documents)

        # Filter extremes
        self.dictionary.filter_extremes(
            no_below=min_count,
            no_above=max_freq,
            keep_n=None
        )

        # Create corpus
        print("Creating corpus...")
        self.corpus = [self.dictionary.doc2bow(doc) for doc in self.processed_documents]

        print(f"Dictionary size: {len(self.dictionary)}")
        print(f"Corpus size: {len(self.corpus)}")

        return self.dictionary, self.corpus